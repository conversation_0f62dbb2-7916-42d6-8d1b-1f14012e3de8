import json
import boto3
from enum import Enum
from typing import Dict, Any, Optional
from langchain_aws import ChatBedrock, ChatBedrockConverse

import boto3
from instructor.client_bedrock import from_bedrock
from pydantic import BaseModel
from config import Config

c = Config()

BEDROCK_TYPE = c.bedrock__runtime
BEDROCK_REGION = c.bedrock__region #"us-west-2"

bedrock_client = boto3.client(
    c.bedrock__runtime,
    region_name=c.bedrock__region
)

class AgentBrain:
    def __init__(
        self,
        model_id: str,
        bedrock_client: Optional[boto3.client] = None,
        region: str = "us-east-1",
        temperature: float = 0.0,
        max_tokens: int = 1024
    ):
        """
        Initialize the AgentBrain with a specific model ID and Bedrock Runtime client.

        :param model_id: The ID of the Bedrock model to use
        :param bedrock_client: Optional boto3 Bedrock Runtime client instance
        :param region: AWS region for Bedrock client
        :param temperature: Sampling temperature for generation
        :param max_tokens: Maximum tokens to generate
        """
        self.model_id = model_id
        self.temperature = temperature
        self.max_tokens = max_tokens

        if bedrock_client:
            self.bedrock_client = bedrock_client
        else:
            session = boto3.Session()
            self.bedrock_client = session.client("bedrock-runtime", region_name=region)

    def _build_request_body(self, prompt: str, model_kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build the request payload for the Bedrock model based on model family.
        """
        # Default model_kwargs
        model_kwargs = model_kwargs or {}

        if self.model_id.startswith("us.anthropic."):
            return {
                "prompt": f"\n\nHuman: {prompt}\n\nAssistant:",
                "max_tokens_to_sample": self.max_tokens,
                "temperature": self.temperature,
                **model_kwargs
            }
        elif self.model_id.startswith("us.ai21."):
            return {
                "prompt": prompt,
                "maxTokens": self.max_tokens,
                "temperature": self.temperature,
                **model_kwargs
            }
        elif self.model_id.startswith("us.cohere."):
            return {
                "prompt": prompt,
                "max_tokens": self.max_tokens,
                "temperature": self.temperature,
                **model_kwargs
            }
        elif self.model_id.startswith("us.meta."):
            return {
                "prompt": prompt,
                "max_gen_len": self.max_tokens,
                "temperature": self.temperature,
                **model_kwargs
            }
        elif self.model_id.startswith("us.amazon."):
            return {
                "messages": [{
                    "role": "user",
                    "content": [{"text": prompt}]
                }],
                "inferenceConfig": {
                    "maxTokens": self.max_tokens,
                    "temperature": self.temperature,
                    **model_kwargs
                }
            }
        else:
            raise ValueError(f"Unsupported model ID: {self.model_id}")

    def _parse_response(self, response_body: Dict[str, Any]) -> str:
        """
        Parse the response from Bedrock based on the model family.
        """
        if self.model_id.startswith("us.anthropic."):
            return response_body.get("completion", "")
        elif self.model_id.startswith("us.ai21."):
            return response_body["completions"][0]["data"]["text"]
        elif self.model_id.startswith("us.cohere."):
            return response_body["generations"][0]["text"]
        elif self.model_id.startswith("us.meta."):
            return response_body.get("generation", "")
        elif self.model_id.startswith("us.amazon."):
            return response_body["output"]["message"]["content"][0]["text"]
        else:
            raise ValueError(f"Unsupported model ID: {self.model_id}")

    def invoke(self, prompt: str, model_kwargs: Optional[Dict[str, Any]] = None) -> str:
        """
        Invoke the Bedrock model with the given prompt and optional parameters.
        """
        body = self._build_request_body(prompt, model_kwargs)

        response = self.bedrock_client.invoke_model(
            modelId=self.model_id,
            body=json.dumps(body)
        )

        response_body = json.loads(response["body"].read().decode("utf-8"))
        return self._parse_response(response_body)

    def set_model(self, model_id: str):
        """Switch to a different model."""
        self.model_id = model_id

class ModelBake(Enum):
    SMALL = c.bedrock__model__nova_pro
    MEDIUM = ""
    LARGE = c.bedrock__model__nova_premier
    NOVA_LITE = c.bedrock__model__nova_lite
    NOVA_PRO = c.bedrock__model__nova_pro
    NOVA_PREMIER = c.bedrock__model__nova_premier
    CLAUDE = c.bedrock__model__sonnet_37

def model_backer(
    model_bake: ModelBake
):
    bedrock_client = boto3.client(c.bedrock__runtime,region_name=c.bedrock__region)
    
    return ChatBedrockConverse(
        model=model_bake.value,
        temperature=0,
        max_tokens=5000,
        client=bedrock_client,
        region_name=c.bedrock__region
        # config = {
        # "performanceConfig" : {
        #     "latency" : "standard | optimized" 
        # }
        # }
    )


def instructor_baker(
    model_bake: ModelBake,
    messages: Any,
    instruction: Any
):
    client = from_bedrock(boto3.client(BEDROCK_TYPE, region_name=BEDROCK_REGION))
    return client.chat.completions.create(
        # model="us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        model=model_bake.value,
        response_model=instruction,
        messages=messages
    )


if __name__ == "__main__":
    # Create Bedrock Runtime client explicitly
    bedrock_client = boto3.client("bedrock-runtime", region_name="us-east-2")

    # Initialize AgentBrain with the client and model
    agent = AgentBrain(
        model_id="us.amazon.nova-pro-v1:0",
        bedrock_client=bedrock_client,
        temperature=0.1,
        max_tokens=4000
    )

    # Example prompt
    prompt = "What model are you?"

    # Invoke the model
    result = agent.invoke(prompt)
    print(result)
