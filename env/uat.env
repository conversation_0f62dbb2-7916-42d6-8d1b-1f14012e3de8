# API MONTHLY LIMIT
MONTHLY_LIMIT = 4000

# CHECKPOINTER
CHECKPOINTER__TYPE = "S3"
CHECKPOINTER__S3_BUCKET = "se-ml-uat"
CHECKPOINTER__s3_REGION = "us-east-1"
CHECKPOINTER__REDIS_CONNECTION = ""

# FILE DOWNLOADER
DOWNLOADED__S3_BUCKET = "se-ml-uat"
DOWNLOADED__S3_PATH = "Elyx_assets"

# BEDROCK FOUNDATION MODEL
BEDROCK__REGION = "us-west-2"
BEDROCK__RUNTIME = "bedrock-runtime"
BEDROCK__MODEL__NOVA_PRO = "us.amazon.nova-pro-v1:0"
BEDROCK__MODEL__NOVA_PREMIER = "us.amazon.nova-premier-v1:0"
BEDROCK__MODEL__NOVA_LITE = "us.amazon.nova-lite-v1:0"
BEDROCK__MODEL__SONNET_35 = "us.anthropic.claude-3-5-sonnet-20240620-v1:0"
BEDROCK__MODEL__SONNET_37 = "us.anthropic.claude-3-7-sonnet-20250219-v1:0"

# BEDROCK GUARDRAIL
GUARDRAIL__REGION = "us-east-1"
GUARDRAIL__RUNTIME = "bedrock-runtime"
GUARDRAIL__ID = "7cl488dpp32g"
GUARDRAIL__VERSION = "DRAFT"
GUARDRAIL__ID2 = "e2oocqzqsif1"
GUARDRAIL__VERSION2 = "DRAFT"

# TAVILY SEARCH
TAVILY_API_KEY = "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"

# EMAIL CREDS
EMAIL__CLIENT_ID = "d787f9af-b9ea-48c2-a898-3bb055890b56"
EMAIL__CLIENT_SECRET = "****************************************"
EMAIL__TENANT_ID = "0beb0c35-9cbb-4feb-99e5-589e415c7944"

