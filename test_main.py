from unittest.mock import patch, MagicMock
from database import DBPool
from agents.logical.rag.build import RA<PERSON>uild
from agents.logical.rag.db import ColumnsRAG
from agents.logical.column_rag.build import ColumnRag
from agents.nodes.bom import *
from agents.nodes.followup import *
from agents.nodes.general import *
from agents.nodes.guard import *
from agents.nodes.refactor import *
from agents.state import BOMState

import json
import types
import pandas as pd
import numpy as np

@patch("database.cx_Oracle.SessionPool")  # patch the SessionPool in database.py
def test_db_pool(mock_session_pool):
    fake_pool = MagicMock()
    mock_session_pool.return_value = fake_pool

    db = DBPool()

    mock_session_pool.assert_called_once()
    assert db.db_pool == fake_pool

@patch("agents.logical.rag.build.faiss.write_index")
@patch("agents.logical.rag.build.faiss.IndexFlatL2")
@patch("agents.logical.rag.build.SentenceTransformer")
@patch("agents.logical.rag.build.pd.read_excel")
def test_build_DB(mock_read_excel, mock_SentenceTransformer, mock_IndexFlatL2, mock_write_index):
    # Create a real mockable DataFrame
    mock_df = pd.DataFrame({
        "COLUMN_NAME": ["Part Number", "Value", "Tolerance"],
        "COLUMN_DESCRIPTION": ["This is a part number", "Capacitance value", "Allowed deviation"],
        "flag": ["a", "b", "c"]
    })

    # Patch read_excel to return the mock_df
    mock_read_excel.return_value = mock_df

    # Patch the SentenceTransformer to return mock embeddings
    mock_model = MagicMock()
    mock_model.encode.return_value = np.random.rand(len(mock_df), 384)
    mock_SentenceTransformer.return_value = mock_model

    # Patch FAISS index
    mock_index = MagicMock()
    mock_IndexFlatL2.return_value = mock_index

    # Run the build_DB logic
    rag = RAGBuild()
    result = rag.build_DB()

    # Assert and verify
    assert result == "success"
    mock_read_excel.assert_called_once()
    mock_model.encode.assert_called_once()
    mock_IndexFlatL2.assert_called_once()
    mock_write_index.assert_called_once()

@patch("agents.logical.rag.db.faiss.read_index")
@patch("agents.logical.rag.db.SentenceTransformer")
@patch("agents.logical.rag.db.pd.read_excel")
def test_columns_rag_prompt(mock_read_excel, mock_SentenceTransformer, mock_read_index):
    # ✅ Mock Excel data (both default and non-default)
    mock_df = pd.DataFrame({
        "TABLE_NAME": ["Products", "Products", "Suppliers"],
        "COLUMN_NAME": ["ID", "Name", "SupplierID"],
        "COLUMN_DESCRIPTION": ["Unique ID", "Product name", "Supplier ID"],
        "TABLE_DESCRIPTION": ["Product data", "Product data", "Supplier info"],
        "flag": ["a", "a", "default"]
    })

    mock_read_excel.return_value = mock_df

    # ✅ Mock FAISS index with dummy .search() result
    mock_index = MagicMock()
    mock_index.search.return_value = (
        None,
        np.array([[0, 1]])  # return row indices
    )
    mock_read_index.return_value = mock_index

    # ✅ Mock model and encode
    mock_model = MagicMock()
    mock_model.encode.return_value = np.random.rand(1, 384)
    mock_SentenceTransformer.return_value = mock_model

    # 🔧 Create ColumnsRAG instance and build prompt
    rag = ColumnsRAG()
    prompt = rag.build_prompt("Give me table details", top_k=2)

    # ✅ Assertions
    assert isinstance(prompt, str)
    assert "table_name" in prompt
    assert "Products" in prompt
    assert "columns" in prompt

    mock_read_excel.assert_called()
    mock_model.encode.assert_called_once()
    mock_index.search.assert_called_once()

@patch("agents.logical.column_rag.build.faiss.write_index")
@patch("agents.logical.column_rag.build.faiss.IndexFlatIP")
@patch("agents.logical.column_rag.build.faiss.normalize_L2")
@patch("agents.logical.column_rag.build.SentenceTransformer")
@patch("agents.logical.column_rag.build.pd.read_excel")
def test_columnrag_build_DB(mock_read_excel, mock_SentenceTransformer, mock_normalize, mock_IndexFlatIP, mock_write_index):
    # 1. Mock DataFrame that simulates Excel input
    mock_df = pd.DataFrame({
        "COLUMN_NAME": ["Part_Number", "Tolerance", "Voltage"],
        "COLUMN_DESCRIPTION": ["Part ID", "Deviation range", "Electric rating"]
    })
    mock_read_excel.return_value = mock_df

    # 2. Mock SentenceTransformer's encode method
    mock_model = MagicMock()
    mock_model.encode.return_value = np.random.rand(len(mock_df), 384)
    mock_SentenceTransformer.return_value = mock_model

    # 3. Mock FAISS index and behavior
    mock_index = MagicMock()
    mock_IndexFlatIP.return_value = mock_index

    # 4. Run the function under test
    rag = ColumnRag()
    result = rag.build_DB()

    # 5. Assertions
    assert result == "success"
    mock_read_excel.assert_called_once()
    mock_model.encode.assert_called_once()
    mock_normalize.assert_called_once()
    mock_IndexFlatIP.assert_called_once()
    mock_write_index.assert_called_once()

def test_check_cost_savings():
    mock_llm = MagicMock()
    mock_llm.return_value = MagicMock(content="false")
    with patch("agents.nodes.bom.llm", mock_llm):
        state = {"question": "Can I save cost?"}
        result = check_cost_savings(state)
        assert result["cost_saving"] is False
        assert result["current_step"] == "cost_saving_checked"

def test_run_rag_agent():
    mock_llm = MagicMock()
    mock_llm.return_value = MagicMock(content="plan: get data from components")
    with patch("agents.nodes.bom.llm", mock_llm):
        state = {"question": "Get me data", "selected_columns": ["column1", "column2"]}
        result = run_rag_agent(state)
        assert "rag_plan" in result
        assert result["current_step"] == "rag_complete"

def test_rag_collector_node():
    mock_llm = MagicMock()
    mock_llm.return_value = MagicMock(content="join tables from question")
    with patch("agents.nodes.bom.model_backer") as mock_model_backer:
        mock_model_backer.return_value = mock_llm
        state = {
            "original_question": "Get details",
            "answering_steps": [],
            "domain_context": {},
            "selected_columns": [],
        }
        result = rag_collector_node(state)
        assert result["current_step"] == "rag_collector_node_complete"
        assert "rag_collector_node" in result["domain_context"]

from unittest.mock import patch, MagicMock

@patch("agents.nodes.bom.llm")
def test_check_requires_data(mock_llm):
    mock_response = MagicMock()
    mock_response.content = "true"
    mock_llm.invoke.return_value = mock_response

    state = {"question": "Does this require BOM data?"}
    result = check_requires_data(state)

    assert result["requires_data"] is True
    assert result["current_step"] == "data_requirement_checked"

@patch("agents.nodes.bom.llm")
def test_run_enhanced_answering_agent(mock_llm):
    mock_response = MagicMock()
    mock_response.content = "Final answer is here."
    mock_llm.invoke.return_value = mock_response

    state = {
        "original_question": "What is tolerance?",
        "refactored_question": "Explain tolerance",
        "bom_data": [{"COLUMN_NAME": "Tolerance", "COLUMN_DESCRIPTION": "Allowed deviation"}],
    }

    result = run_enhanced_answering_agent(state)

    assert result["current_step"] == "enhanced_answer_complete"
    assert result["final_answer"] == "Final answer is here."
    assert isinstance(result["conversation_history"], list)
    assert result["conversation_history"][0]["answer"] == "Final answer is here."

@patch("agents.nodes.followup.instructor_baker")
def test_follow_up_check(mock_instructor_baker):
    # Mock instructor_baker to return a FollowUpModelOutput-like response
    mock_response = MagicMock()
    mock_response.followup = True
    mock_instructor_baker.return_value = mock_response

    state = {
        "question": "What about lead time?",
        "conversation_id": "conv1"
    }

    # Fake checkpointer
    fake_cp = MagicMock()
    fake_cp.checkpoint = {"channel_values": {"question": "What is MOQ?", "final_answer": "Minimum Order Quantity"}}
    fake_checkpointer = MagicMock()
    fake_checkpointer.list.return_value = [fake_cp]

    updated_state = follow_up_check(state, fake_checkpointer)

    assert updated_state["is_followup"] is True
    assert updated_state["current_step"] == "followup_check_complete"

@patch("agents.nodes.followup.get_chat_history_local_file")
@patch("agents.nodes.followup.llm")
def test_follow_up_process(mock_llm, mock_get_chat_history):
    mock_response = MagicMock()
    mock_response.content = "What is the MOQ and lead time for this part?"
    mock_llm.invoke.return_value = mock_response

    # Mock history message structure
    mock_history = MagicMock()
    mock_history.messages = [
        types.SimpleNamespace(type="human", content="What is MOQ?"),
        types.SimpleNamespace(type="ai", content="Minimum Order Quantity")
    ]
    mock_get_chat_history.return_value = mock_history

    fake_cp = MagicMock()
    fake_cp.checkpoint = {"channel_values": {"question": "What is MOQ?", "final_answer": "Minimum Order Quantity"}}
    fake_checkpointer = MagicMock()
    fake_checkpointer.list.return_value = [fake_cp]

    state = {
        "question": "And what about lead time?",
        "conversation_id": "conv1"
    }

    updated_state = follow_up_process(state, fake_checkpointer)

    assert updated_state["question"] == "What is the MOQ and lead time for this part?"
    assert updated_state["is_followup"] is True
    assert updated_state["current_step"] == "followup_check_complete"
    assert "conversation_summary" in updated_state
    assert isinstance(updated_state["conversation_history"], list)

def test_safe_should_process_followup():
    state_followup = {"is_followup": True}
    state_continue = {"is_followup": False}

    assert safe_should_process_followup(state_followup) == "followup"
    assert safe_should_process_followup(state_continue) == "continue"

@patch("agents.nodes.general.columnsDescriptionRAG")
@patch("agents.nodes.general.llm")
def test_run_general_answering_agent(mock_llm, mock_columnsDescriptionRAG):
    # ✅ Setup
    question = "What is a typical lead time?"
    mock_context = "COLUMN_NAME: lead time\nCOLUMN_DESCRIPTION: Expected delivery duration"

    # ✅ Mock context generator
    mock_columnsDescriptionRAG.build_prompt.return_value = mock_context

    # ✅ Mock LLM response
    mock_response = MagicMock()
    mock_response.content = "Lead time refers to the time between placing an order and receiving it."
    mock_llm.invoke.return_value = mock_response

    # ✅ Mock state
    state = {
        "question": question,
        "conversation_id": "conv1"
    }

    # 🔧 Run the agent
    updated_state = run_general_answering_agent(state)

    # ✅ Assertions
    assert updated_state["final_answer"] == mock_response.content
    assert updated_state["current_step"] == "general_answer_complete"
    assert isinstance(updated_state["conversation_history"], list)
    assert updated_state["conversation_history"][-1]["type"] == "general_query"
    assert updated_state["conversation_history"][-1]["data_used"] is False
    assert "Lead time" in updated_state["final_answer"]
    mock_llm.invoke.assert_called_once()
    mock_columnsDescriptionRAG.build_prompt.assert_called_once_with(question, top_k=5)

@patch("agents.nodes.guard._guardrail_run")
@patch("agents.nodes.guard.instructor_baker")
def test_guardrail_check_question_scope(mock_instructor_baker, mock_guardrail_run):
    # ✅ Mock the guardrail output to be successful
    mock_guardrail_run.return_value = True

    # ✅ Mock GuardModelOutput-like return
    mock_guard_response = MagicMock()
    mock_guard_response.in_scope = True
    mock_guard_response.response = "Allowed"
    mock_guard_response.reason = "Contains BOM terms"
    mock_instructor_baker.return_value = mock_guard_response

    # ✅ Prepare initial state
    state = {
        "question": "What is the lead time for a capacitor?",
        "conversation_id": "conv-test"
    }

    # 🔧 Call the function
    updated_state = guardrail_check_question_scope(state)

    # ✅ Assertions
    assert updated_state["in_scope"] is True
    assert updated_state["current_step"] == "guardrail_check_complete"
    assert "scope_reason" in updated_state
    assert updated_state["scope_reason"] == "Contains BOM terms"
    mock_guardrail_run.assert_called_once()
    mock_instructor_baker.assert_called_once()

@patch("agents.nodes.refactor.c.logger")
def test_initialize_question_state(mock_logger):
    state = {"original_question": "What parts are risky?"}
    new_state = initialize_question_state(state)
    assert new_state["question"] == "What parts are risky?"
    assert new_state["current_step"] == "question_initialized"
    assert isinstance(new_state["answering_steps"], list)

@patch("agents.nodes.refactor.instructor_baker")
def test_question_planner_node(mock_instructor):
    mock_response = MagicMock()
    mock_response.refactored_question = "What are risky parts?"
    mock_response.clarification_message = "None needed"
    mock_response.needs_clarification = False
    mock_instructor.return_value = mock_response

    state = {
        "original_question": "Which parts are risky?",
        "conversation_summary": "Past Q&A summary"
    }
    result = question_planner_node(state)
    assert result["question"] == "What are risky parts?"
    assert result["refactored_question"] == "What are risky parts?"
    assert result["clarification_message"] == "None needed"
    assert result["needs_clarification"] is False
    mock_instructor.assert_called_once()

@patch("agents.nodes.refactor.llm")
def test_safe_question_refactor(mock_llm):
    mock_llm.invoke.return_value.content = json.dumps({
        "refactored_question": "List risky components by lifecycle.",
        "confidence_score": 0.9,
        "complexity": "moderate"
    })

    state = BOMState({
        "original_question": "What components are risky?",
        "question": "What components are risky?",
        "conversation_history": [{"question": "A?", "answer": "B"}]
    })

    result = safe_question_refactor(state)
    assert result["question"] == "List risky components by lifecycle."
    assert result["confidence_score"] == 0.9
    assert result["question_complexity"] == "moderate"
    assert result["current_step"] == "question_planned"

@patch("agents.nodes.refactor.llm")
def test_safe_generate_steps(mock_llm):
    mock_llm.invoke.return_value.content = json.dumps({
        "steps": [
            {
                "step_number": 1,
                "action": "Analyze BOM",
                "description": "Review part details",
                "data_requirements": ["part_number", "status"]
            },
            {
                "step_number": 2,
                "action": "Assess Risk",
                "description": "Determine lifecycle status",
                "data_requirements": ["risk_grade"]
            }
        ],
        "total_steps": 2
    })

    state = BOMState({
        "question": "What components are risky?",
        "question_complexity": "moderate",
        "requires_decomposition": True
    })

    result = safe_generate_steps(state)
    assert result["answering_steps"][0] == "Analyze BOM"
    assert "risk_grade" in result["domain_context"]["data_requirements"]
    assert result["domain_context"]["total_steps"] == 2
    assert result["current_step"] == "steps_generated"

