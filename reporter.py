import pytz
import threading
from datetime import datetime, timedelta, timezone
from tools.emails.utils import send_email
from tools.system.counter import load_daily_counts

def generate_daily_usage_email_html(daily_counts):
    today = daily_counts.get("date", "unknown")
    general_count = daily_counts.get("GENERAL", 0)
    conversation_counts = daily_counts.get("conversations", {})

    html_body = f"""
    <html>
    <head>
        <style>
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
            }}
            h2 {{
                color: #2E86C1;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                max-width: 600px;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
        </style>
    </head>
    <body>
        <h2>Daily Usage Report for {today}</h2>
        <p><strong>General count:</strong> {general_count}</p>
        <h3>Conversation counts:</h3>
        <table>
            <thead>
                <tr>
                    <th>Conversation ID</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
    """

    if conversation_counts:
        for conv_id, count in conversation_counts.items():
            html_body += f"""
                <tr>
                    <td>{conv_id}</td>
                    <td>{count}</td>
                </tr>
            """
    else:
        html_body += """
            <tr>
                <td colspan="2">No conversation data available.</td>
            </tr>
        """

    html_body += """
            </tbody>
        </table>
    </body>
    </html>
    """
    return html_body


def send_daily_usage_email():
    daily_counts = load_daily_counts()
    body = generate_daily_usage_email_html(daily_counts)
    # send_email("Daily Usage Report", body)
    send_email(
        [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ],
        'RFQ - API Quote',
        body
    )

def schedule_daily_email():
    cairo_tz = pytz.timezone("Africa/Cairo")
    now = datetime.now(cairo_tz)
    print(now)
    target_time = now.replace(hour=12, minute=47, second=0, microsecond=0)
    if now > target_time:
        target_time += timedelta(days=1)
    delay = (target_time - now).total_seconds()
    threading.Timer(delay, run_daily_email_task).start()

def run_daily_email_task():
    send_daily_usage_email()
    schedule_daily_email()

# Start the scheduling when your app starts
schedule_daily_email()