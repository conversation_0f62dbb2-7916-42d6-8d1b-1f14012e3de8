import requests
import uuid
import time
import json
import pandas as pd
from datetime import datetime
import os

# Ensure results folder exists
os.makedirs("results", exist_ok=True)

# Load scenarios
with open("scenarios.json") as f:
    scenarios = json.load(f)

# Base input template
def build_payload(message_text, bom_id, question_id, conversation_id):
    return {
        "authorizationInfo": {
            "authorizationType": "4",
            "authorizationToken": ""
        },
        "conversationInfo": {
            "id": conversation_id,
            "type": None
        },
        "messageText": message_text,
        "messageReceiveTimestamp": None,
        "questionId": question_id,
        "bomId": bom_id,
        "optimizerId": None
    }

# Test function
def test_api(api_url, name):
    rows = []
    test_start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    for scenario_name, scenario in scenarios.items():
        bom_id = scenario["bomId"]
        conversation_id = f"test_{str(uuid.uuid4().hex)}"
        for idx, message in enumerate(scenario["messages"], start=1):
            print(f"scenario: {scenario_name}")
            print(f"message: {message}")
            payload = build_payload(message, bom_id, idx, conversation_id)
            start_time = time.time()
            try:
                response = requests.post(api_url, json=payload, timeout=30)
                end_time = time.time()
                elapsed = round(end_time - start_time, 2)

                message_text = response.json().get("messageText", "NO messageText in response")

            except Exception as e:
                elapsed = round(time.time() - start_time, 2)
                message_text = f"Error: {str(e)}"

            print(f"conversation id: {payload['conversationInfo']['id']}")
            print(f"response: {message_text}")
            print("\n=======================\n")
            rows.append({
                "timestamp": test_start_time,
                "scenario": scenario_name,
                "conversation_id": payload["conversationInfo"]["id"],
                "question": message,
                "response": message_text,
                "time_sec": elapsed
            })

            time.sleep(0.5)  # optional delay
            #break
        break

    df = pd.DataFrame(rows)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"results/{name}_results_{timestamp}.xlsx"
    df.to_excel(output_file, index=False)
    print(f"[✓] Saved: {output_file}")

# Run both apis
#test_api("http://172.30.5.133:5500/message", "api_5500")
#test_api("http://172.30.5.133:5502/message", "api_5502")

