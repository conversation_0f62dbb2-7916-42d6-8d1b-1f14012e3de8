import os
import logging

from dotenv import load_dotenv
load_dotenv(override=True)

# check the env type from Devops
env_type = os.getenv('ENV_TYPE', 'production') # production

# based on the env var, we will load the env file
env_file = {
    'production': 'prod.env',
    'dev': 'dev.env',
    'quality': 'quality.env',
    'uat': 'uat.env'
}.get(env_type, 'prod.env')  # Default to dev.env if ENV_TYPE is unknown

load_dotenv(os.path.join('env', env_file), override=True) # load env

class Config:
    # Memory Checkpointer
    checkpointer__type = os.getenv("CHECKPOINTER__TYPE")
    checkpointer__s3_bucket = os.getenv("CHECKPOINTER__S3_BUCKET")
    checkpointer__s3_region = os.getenv("CHECKPOINTER__s3_REGION")
    checkpointer__redis_connection = os.getenv("CHECKPOINTER__REDIS_CONNECTION")
    
    # Bedrock Configs
    bedrock__region = os.getenv("BEDROCK__REGION")
    bedrock__runtime = os.getenv("BEDROCK__RUNTIME")
    bedrock__model__nova_pro = os.getenv("BEDROCK__MODEL__NOVA_PRO")
    bedrock__model__nova_premier = os.getenv("BEDROCK__MODEL__NOVA_PREMIER")
    bedrock__model__nova_lite = os.getenv("BEDROCK__MODEL__NOVA_LITE")
    bedrock__model__sonnet_35 = os.getenv("BEDROCK__MODEL__SONNET_35")
    bedrock__model__sonnet_37 = os.getenv("BEDROCK__MODEL__SONNET_37")
    
    # File Download
    downloaded__s3_bucket = os.getenv("DOWNLOADED__S3_BUCKET")
    downloaded__s3_path = os.getenv("DOWNLOADED__S3_PATH")
    
    # Database Info
    DB_USER = os.getenv("DB_USER")
    DB_PASS = os.getenv("DB_PASS")
    DB_HOST = os.getenv("DB_HOST")
    DB_PORT = os.getenv("DB_PORT")
    DB_SERVICE_NAME = os.getenv("DB_SERVICE_NAME")

    db__connection_string = f"{DB_USER}/{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_SERVICE_NAME}"

    # Logger
    logging.basicConfig(level=logging.INFO)
    # logger = logging.getLogger(__name__)
    
    # AWS Bedrock Guardrail
    guardrail__region = os.getenv("GUARDRAIL__REGION")
    guardrail__runtime = os.getenv("GUARDRAIL__RUNTIME")
    guardrail__id = os.getenv("GUARDRAIL__ID")
    guardrail__version = os.getenv("GUARDRAIL__VERSION")
    guardrail__configs = [
        {"id": os.getenv("GUARDRAIL__ID"), "version": os.getenv("GUARDRAIL__VERSION")},
        {"id": os.getenv("GUARDRAIL__ID2"), "version": os.getenv("GUARDRAIL__VERSION2")}
    ]
    
    # langsmith
    LANGSMITH_TRACING = os.getenv("LANGSMITH_TRACING")
    LANGSMITH_ENDPOINT = os.getenv("LANGSMITH_ENDPOINT")
    LANGSMITH_API_KEY = os.getenv("LANGSMITH_API_KEY")
    LANGSMITH_PROJECT = os.getenv("LANGSMITH_PROJECT")
    
    # langfuse
    langfuse__secret_key = os.getenv("LANGFUSE_SECRET_KEY")
    langfuse__public_key = os.getenv("LANGFUSE_PUBLIC_KEY")
    langfuse__host = os.getenv("LANGFUSE_HOST")
        
    # tavily search
    TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")
    
    # Email credentials
    email__client_id = os.getenv("EMAIL__CLIENT_ID")
    email__client_secret = os.getenv("EMAIL__CLIENT_SECRET")
    email__tenant_id = os.getenv("EMAIL__TENANT_ID")

    # logs path
    logs_folder = "/data/b001/app/bom_ai_agent/logs/SQL_Logs"
    conversations_folder = "/data/b001/app/bom_ai_agent/logs/conversations"
    MONTHLY_COUNT_FILE = "/data/b001/app/bom_ai_agent/logs/monthly_counts.json"

    MONTHLY_LIMIT = int(os.getenv("MONTHLY_LIMIT"))
    
    def logger(self, name: str):
        return logging.getLogger(name)
    