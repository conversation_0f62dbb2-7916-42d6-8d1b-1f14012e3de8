#!/bin/sh

# Check if the virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Virtual environment not found. Creating..."
    python -m venv .venv
    echo "Installing requirements..."
    .venv/bin/pip install --upgrade pip
    .venv/bin/pip install -r requirements.txt
else
    echo "Virtual environment found. Activating..."
fi

# Activate the virtual environment
. .venv/bin/activate

# Export Environment variables

# Run the app
gunicorn -w 16 -k uvicorn.workers.UvicornWorker api:app --bind 0.0.0.0:5500

 