aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
altair
annotated-types==0.7.0
anyio==4.9.0
astor==0.8.1
asttokens==3.0.0
async-timeout==4.0.3
attrs==25.3.0
beautifulsoup4==4.13.4
blinker==1.9.0
boto3==1.39.14
botocore==1.36.0
cachetools==5.5.2
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
cryptography
cx-Oracle==8.3.0
cycler==0.12.1
dataclasses-json==0.6.7
debugpy==1.8.14
decorator==5.2.1
distro==1.9.0
duckdb==1.2.2
et_xmlfile==2.0.0
exceptiongroup==1.3.0
executing==2.2.0
faiss-cpu==1.11.0
Faker==19.13.0
fastapi==0.115.12
filelock==3.18.0
fonttools==4.58.0
frozenlist==1.6.0
fsspec==2025.3.2
gitdb==4.0.12
GitPython==3.1.44
greenlet==3.2.2
gunicorn==23.0.0
h11==0.16.0
hf-xet==1.1.0
httpcore==1.0.9
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.31.1
idna==3.10
jedi==0.19.2
Jinja2==3.1.6
jiter
jmespath==1.0.1
joblib==1.5.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
langchain==0.3.13
langchain-aws==0.2.11
langchain-community==0.3.13
langchain-core==0.3.28
langchain-text-splitters==0.3.4
langgraph==0.2.60
langgraph-checkpoint==2.0.9
langgraph-sdk==0.1.48
langsmith==0.1.147
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib
mdurl==0.1.2
mpmath==1.3.0
msal==1.32.3
msgpack==1.1.0
multidict==6.4.3
mypy_extensions==1.1.0
narwhals==1.38.2
nest-asyncio==1.6.0
numpy==1.26.4
nvidia-cublas-cu12==12.1.3.1
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==9.1.0.70
nvidia-cufft-cu12==11.0.2.54
nvidia-curand-cu12==10.3.2.106
nvidia-cusolver-cu12==11.4.5.107
nvidia-cusparse-cu12==12.1.0.106
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.9.41
nvidia-nvtx-cu12==12.1.105
o365==2.1.0
openai==1.78.0
openpyxl==3.1.5
orjson==3.10.18
packaging==24.2
pandas==1.5.3
pandasai==2.4.2
parso==0.8.4
pexpect==4.9.0
pillow==10.4.0
platformdirs==4.3.8
prompt_toolkit==3.0.51
propcache==0.3.1
protobuf==5.29.4
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==20.0.0
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.9.1
pydantic_core==2.27.2
pydeck==0.9.1
Pygments==2.19.1
PyJWT==2.10.1
pyparsing==3.2.3
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
pytz==2025.2
PyYAML==6.0.2
pyzmq==26.4.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.24.0
s3transfer==0.11.3
safetensors==0.5.3
scikit-learn==1.6.1
scipy
sentence-transformers==4.1.0
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.40
sqlglot==25.34.1
sqlglotrs==0.3.0
stack-data==0.6.3
starlette==0.46.2
streamlit==1.41.1
sympy==1.14.0
tabulate==0.9.0
tavily-python==0.5.0
tenacity==9.1.2
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
toml==0.10.2
torch==2.4.1
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.51.3
triton==3.0.0
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
tzlocal==5.3.1
urllib3
uvicorn==0.34.2
watchdog==6.0.0
wcwidth==0.2.13
yarl==1.20.0
instructor==1.8.3
eval-type-backport==0.2.2
redis==6.2.0
langfuse==3.0.3
pytest==8.4.1
pytest-cov==6.2.1