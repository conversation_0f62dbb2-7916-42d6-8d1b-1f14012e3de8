from langchain_community.chat_message_histories import FileChatMessageHistory

def get_chat_history_local_file(conversation_id: str, conversation_dir: str = "conversations") -> FileChatMessageHistory:
    import os
    from pathlib import Path
    conversation_dir = Path(conversation_dir)
    if not conversation_dir.exists():
        conversation_dir.mkdir(parents=True)
    file_path = conversation_dir / f"{conversation_id}.json"
    return FileChatMessageHistory(str(file_path))
