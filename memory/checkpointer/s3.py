import os
import json
import time
import boto3
import asyncio
import datetime
from botocore.exceptions import ClientError
from typing import (
    Any, Dict, Optional, Generator,
    Sequence, Tuple, AsyncGenerator
)
from langgraph.checkpoint.base import (
    BaseCheckpointSaver, Checkpoint, CheckpointMetadata,
    CheckpointTuple, SerializerProtocol
)
from langchain_core.runnables import RunnableConfig
# LangGraph imports
from langgraph.checkpoint.serde.jsonplus import JsonPlusSerializer #[3]

from config import Config
c = Config()

class S3Checkpointer(BaseCheckpointSaver):
    """
    Stores LangGraph checkpoints in an S3 bucket using the BaseCheckpointSaver interface.

    Attributes:
        bucket_name (str): The name of the S3 bucket.
        prefix (str): Optional prefix for S3 keys. Defaults to "langgraph_checkpoints/".
        s3_client: The Boto3 S3 client instance.
        serde (SerializerProtocol): The serializer/deserializer for checkpoint data.
    """
    def __init__(self, bucket_name: str, region: str, prefix: str = "langgraph_checkpoints/", *, serde: Optional[SerializerProtocol] = None):
        # Use the imported JsonPlusSerializer from the correct path
        super().__init__(serde=serde or JsonPlusSerializer()) #[3]
        self.bucket_name = bucket_name
        self.region = region
        if not prefix.endswith("/"):
            prefix += "/"
        self.prefix = prefix
        self.s3_client = boto3.client('s3', region_name=self.region)
        c.logger(__name__).info("S3C> S3 Checkpointer Init")
        # self.aio_session = aioboto3.Session() # For true async
        self.conn = None # Interface compliance

    def _get_s3_key(self, thread_id: str, checkpoint_id: str) -> str:
        """Constructs the S3 key for a checkpoint."""
        return f"{self.prefix}{thread_id}/checkpoint_{checkpoint_id}.json"

    def _get_writes_s3_key(self, thread_id: str, task_id: str) -> str:
        """Constructs the S3 key for pending writes."""
        return f"{self.prefix}{thread_id}/writes_{task_id}.json"

    def _parse_checkpoint_id(self, s3_key: str) -> Optional[str]:
        """Extracts checkpoint_id from an S3 key."""
        try:
            # Ensure robustness against different key formats if necessary
            filename = s3_key.split('/')[-1]
            if filename.startswith('checkpoint_') and filename.endswith('.json'):
                return filename[len('checkpoint_'):-len('.json')]
        except IndexError:
            pass
        return None

    # --- Sync Methods ---

    def get_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        """Retrieves a checkpoint tuple from S3."""
        thread_id = config["configurable"]["thread_id"]
        checkpoint_id = config["configurable"].get("checkpoint_id")
        checkpoint_obj_bytes: Optional[bytes] = None
        resolved_checkpoint_id: Optional[str] = checkpoint_id

        if checkpoint_id:
            # Get specific checkpoint
            key = self._get_s3_key(thread_id, checkpoint_id)
            try:
                response = self.s3_client.get_object(Bucket=self.bucket_name, Key=key)
                checkpoint_obj_bytes = response['Body'].read()
            except ClientError as e:
                c.logger(__name__).info(f"S3C> S3 Checkpointer get_tupes {e}")
                if e.response['Error']['Code'] == 'NoSuchKey':
                    return None # Not found
                else:
                    print(f"S3 Error getting specific checkpoint {key}: {e}")
                    raise
        else:
            # Get the latest checkpoint
            try:
                list_response = self.s3_client.list_objects_v2(
                    Bucket=self.bucket_name,
                    Prefix=f"{self.prefix}{thread_id}/checkpoint_"
                )
                contents = list_response.get('Contents')
                if not contents:
                    return None # No checkpoints found

                latest_obj_summary = max(contents, key=lambda x: x['LastModified'])
                key = latest_obj_summary['Key']
                response = self.s3_client.get_object(Bucket=self.bucket_name, Key=key)
                checkpoint_obj_bytes = response['Body'].read()
                resolved_checkpoint_id = self._parse_checkpoint_id(key) # Get ID from the found key

            except ClientError as e:
                c.logger(__name__).info(f"S3C> S3 Checkpointer get_tupes {e}")
                print(f"S3 Error listing/getting latest checkpoint for thread {thread_id}: {e}")
                return None # Or raise depending on desired behavior

        if checkpoint_obj_bytes:
            try:
                saved_dict = self.serde.loads(checkpoint_obj_bytes)

                # Update config with the ID that was actually loaded (important if latest was fetched)
                loaded_config = config.copy()
                if resolved_checkpoint_id:
                    loaded_config['configurable']['checkpoint_id'] = resolved_checkpoint_id

                # Note on pending writes: CheckpointTuple expects this field.
                # How pending writes are handled depends on the LangGraph version and flow.
                # If writes aren't saved *within* the checkpoint blob by `put`,
                # you might need separate logic here to fetch them from their S3 keys (`writes_*`)
                # based on the context (e.g., the checkpoint's task ID or metadata).
                # For simplicity, we check if they were included in the saved blob.
                pending_writes = saved_dict.get("pending_writes", [])

                return CheckpointTuple(
                    config=loaded_config,
                    checkpoint=saved_dict["checkpoint"],
                    metadata=saved_dict.get("metadata"), # Use .get() for flexibility
                    pending_writes=pending_writes,
                )
            except Exception as e:
                c.logger(__name__).info(f"S3C> S3 Checkpointer get_tupes {e}")
                print(f"Error deserializing checkpoint content from key {key}: {e}")
                # Decide how to handle corrupt data - skip, raise, log?
                return None # Or raise
        return None


    def list(
        self,
        config: Optional[RunnableConfig],
        *,
        filter: Optional[Dict[str, Any]] = None, # Filter implementation depends on metadata structure
        before: Optional[RunnableConfig] = None,
        limit: Optional[int] = None,
    ) -> Generator[CheckpointTuple, None, None]:
        """Lists checkpoint tuples for a thread_id, supports filtering and limits."""
        if not config or "thread_id" not in config.get("configurable", {}):
            raise ValueError("Config with thread_id must be provided")
        thread_id = config["configurable"]["thread_id"]

        paginator = self.s3_client.get_paginator('list_objects_v2')
        page_iterator = paginator.paginate(
            Bucket=self.bucket_name,
            Prefix=f"{self.prefix}{thread_id}/checkpoint_"
        )

        all_checkpoints_summary = []
        try:
            for page in page_iterator:
                for obj_summary in page.get('Contents', []):
                    all_checkpoints_summary.append({
                        'Key': obj_summary['Key'],
                        'LastModified': obj_summary['LastModified']
                    })
        except ClientError as e:
            c.logger(__name__).info(f"S3C> S3 Checkpointer list {e}")
            print(f"Error listing S3 objects for thread {thread_id}: {e}")
            return # Stop iteration on error

        # Sort by timestamp descending (most recent first)
        all_checkpoints_summary.sort(key=lambda x: x['LastModified'], reverse=True)

        # Apply 'before' filter (requires fetching timestamp of 'before' checkpoint)
        before_ts = None
        if before:
            before_ts = self._get_checkpoint_ts(before)

        filtered_checkpoints = []
        for summary in all_checkpoints_summary:
            if before_ts and summary['LastModified'] >= before_ts:
                continue
            filtered_checkpoints.append(summary)

        # Apply limit
        if limit is not None:
            filtered_checkpoints = filtered_checkpoints[:limit]

        # Yield CheckpointTuples by fetching actual content
        count = 0
        for summary in filtered_checkpoints:
            if limit is not None and count >= limit:
                break
            try:
                response = self.s3_client.get_object(Bucket=self.bucket_name, Key=summary['Key'])
                checkpoint_bytes = response['Body'].read()
                saved = self.serde.loads(checkpoint_bytes)
                chk_id = self._parse_checkpoint_id(summary['Key'])
                if chk_id is None: continue # Skip if ID parsing fails

                # Construct config for this specific checkpoint
                chk_config = {"configurable": {"thread_id": thread_id, "checkpoint_id": chk_id}}

                yield CheckpointTuple(
                    config=chk_config,
                    checkpoint=saved["checkpoint"],
                    metadata=saved.get("metadata"),
                    pending_writes=saved.get("pending_writes", []),
                )
                count += 1
            except ClientError as e:
                c.logger(__name__).info(f"S3C> S3 Checkpointer list {e}")
                print(f"Error fetching checkpoint content {summary['Key']} during list: {e}")
            except Exception as e:
                c.logger(__name__).info(f"S3C> S3 Checkpointer list {e}")
                print(f"Error deserializing checkpoint {summary['Key']} during list: {e}")


    def put(
        self,
        config: RunnableConfig,
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata,
        *args,
        **kwargs
    ) -> RunnableConfig:
        """Saves a checkpoint and metadata to S3."""
        thread_id = config["configurable"]["thread_id"]
        checkpoint_id = checkpoint.get("id") # LangGraph v0.2+ puts ID here
        if not checkpoint_id:
            # Fallback for older versions or if 'id' is missing
            checkpoint_id = datetime.datetime.now(datetime.timezone.utc).isoformat()
            print(f"Warning: Checkpoint ID missing, generated: {checkpoint_id}")

        key = self._get_s3_key(thread_id, checkpoint_id)

        data_to_save = {
            "checkpoint": checkpoint,
            "metadata": metadata,
             # Decide whether pending writes should be cleared and saved separately by put_writes,
             # or bundled here if the Checkpoint object passed includes them.
             # "pending_writes": checkpoint.get("pending_writes")
        }

        try:
            # c.logger(__name__).info(f"S3C> Saving checkpoing = {json.dumps(checkpoint, indent=2, default=str)}")
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=key,
                Body=self.serde.dumps(data_to_save),
                ContentType='application/json',
                StorageClass='STANDARD_IA'
            )
            # Return config updated with the checkpoint_id used
            updated_config = config.copy()
            updated_config['configurable']['checkpoint_id'] = checkpoint_id
            return updated_config
        except ClientError as e:
            c.logger(__name__).info(f"S3C> S3 Checkpointer put {e}")
            raise RuntimeError(f"Failed to save checkpoint {checkpoint_id} for thread {thread_id} to S3: {e}")


    def put_writes(self, config: RunnableConfig, writes: Sequence[Tuple[str, Any]], task_id: str) -> None:
        """Saves intermediate writes for a task to S3."""
        thread_id = config["configurable"]["thread_id"]
        key = self._get_writes_s3_key(thread_id, task_id)

        writes_data = {
            "writes": writes,
            "task_id": task_id,
            "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat()
        }

        try:
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=key,
                Body=self.serde.dumps(writes_data),
                ContentType='application/json',
                StorageClass='STANDARD_IA'
            )
            # Optional: Logic to clean up previous writes for the same task/thread if needed
        except ClientError as e:
            c.logger(__name__).info(f"S3C> S3 Checkpointer put_writes {e}")
            # Decide on error handling: log, raise, etc.
            print(f"Warning: Failed to save writes for task {task_id}, thread {thread_id}: {e}")

    # --- Async Methods (basic asyncio wrappers) ---

    async def aget_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        return await asyncio.to_thread(self.get_tuple, config)

    async def alist(
        self,
        config: Optional[RunnableConfig],
        *,
        filter: Optional[Dict[str, Any]] = None,
        before: Optional[RunnableConfig] = None,
        limit: Optional[int] = None,
    ) -> AsyncGenerator[CheckpointTuple, None]:
        # Converting sync generator to async is tricky. This collects all then yields.
        # For true async streaming, aioboto3 and async iteration logic are needed.
        sync_gen = self.list(config, filter=filter, before=before, limit=limit)
        try:
            results = await asyncio.to_thread(list, sync_gen)
            for item in results:
                yield item
        except Exception as e:
            c.logger(__name__).info(f"S3C> S3 Checkpointer alist {e}")
            # Catch potential errors during the sync list execution
            print(f"Error during async list execution: {e}")
            # Decide how to handle: raise, log, yield nothing

    async def aput(self, config: RunnableConfig, checkpoint: Checkpoint, metadata: CheckpointMetadata) -> RunnableConfig:
        return await asyncio.to_thread(self.put, config, checkpoint, metadata)

    async def aput_writes(self, config: RunnableConfig, writes: Sequence[Tuple[str, Any]], task_id: str) -> None:
        await asyncio.to_thread(self.put_writes, config, writes, task_id)

    # --- Helper Method ---
    def _get_checkpoint_ts(self, config: RunnableConfig) -> Optional[datetime.datetime]:
        """ Gets the LastModified timestamp of a checkpoint specified by config. """
        thread_id = config["configurable"].get("thread_id")
        checkpoint_id = config["configurable"].get("checkpoint_id")
        if not thread_id or not checkpoint_id:
            print("Warning: Cannot get timestamp without thread_id and checkpoint_id in config")
            return None

        key = self._get_s3_key(thread_id, checkpoint_id)
        try:
            # Use head_object for efficiency - only need metadata
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=key)
            return response['LastModified']
        except ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                print(f"Warning: Checkpoint {key} not found for timestamp lookup.")
            else:
                print(f"S3 Error getting head for {key}: {e}")
            return None

