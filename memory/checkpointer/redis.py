import redis
import json
import datetime
from typing import Optional, Dict, Any, Sequence, Tuple, Generator
from langgraph.checkpoint.base import BaseCheckpointSaver, CheckpointTuple
from langgraph.checkpoint.serde.jsonplus import JsonPlusSerializer
from langchain_core.runnables import RunnableConfig

class RedisC<PERSON>ckpointer(BaseCheckpointSaver):
    """
    Redis-based checkpointer that mirrors S3Checkpointer functionality.
    
    Attributes:
        redis_client: Redis client instance
        prefix: Key prefix for Redis keys
        serde: Serializer for checkpoint data
        ttl: Optional TTL for checkpoints in seconds
    """
    
    def __init__(self, redis_url: str, prefix: str = "langgraph_checkpoints:", 
                 ttl: Optional[int] = None, serde=None):
        super().__init__(serde=serde or JsonPlusSerializer())
        self.redis_client = redis.from_url(redis_url)
        self.prefix = prefix if prefix.endswith(":") else f"{prefix}:"
        self.ttl = ttl
        
    def _get_redis_key(self, thread_id: str, checkpoint_id: str) -> str:
        """Constructs Redis key for a checkpoint."""
        return f"{self.prefix}{thread_id}:checkpoint:{checkpoint_id}"
    
    def _get_writes_redis_key(self, thread_id: str, task_id: str) -> str:
        """Constructs Redis key for pending writes."""
        return f"{self.prefix}{thread_id}:writes:{task_id}"
    
    def _get_thread_pattern(self, thread_id: str) -> str:
        """Gets pattern for listing thread checkpoints."""
        return f"{self.prefix}{thread_id}:checkpoint:*"

    def get_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        """Retrieves a checkpoint tuple from Redis."""
        thread_id = config["configurable"]["thread_id"]
        checkpoint_id = config["configurable"].get("checkpoint_id")
        
        if checkpoint_id:
            # Get specific checkpoint
            key = self._get_redis_key(thread_id, checkpoint_id)
            checkpoint_data = self.redis_client.get(key)
            if not checkpoint_data:
                return None
        else:
            # Get latest checkpoint
            pattern = self._get_thread_pattern(thread_id)
            keys = self.redis_client.keys(pattern)
            if not keys:
                return None
            
            # Sort by timestamp (assuming checkpoint_id contains timestamp)
            latest_key = max(keys, key=lambda k: k.decode('utf-8').split(':')[-1])
            checkpoint_data = self.redis_client.get(latest_key)
            checkpoint_id = latest_key.decode('utf-8').split(':')[-1]
        
        if checkpoint_data:
            try:
                saved_dict = self.serde.loads(checkpoint_data)
                loaded_config = config.copy()
                loaded_config['configurable']['checkpoint_id'] = checkpoint_id
                
                return CheckpointTuple(
                    config=loaded_config,
                    checkpoint=saved_dict["checkpoint"],
                    metadata=saved_dict.get("metadata"),
                    pending_writes=saved_dict.get("pending_writes", []),
                )
            except Exception as e:
                print(f"Error deserializing checkpoint: {e}")
                return None
        return None

    def put(self, config: RunnableConfig, checkpoint, metadata, *args, **kwargs) -> RunnableConfig:
        """Saves a checkpoint to Redis."""
        thread_id = config["configurable"]["thread_id"]
        checkpoint_id = checkpoint.get("id") or datetime.datetime.now(datetime.timezone.utc).isoformat()
        
        key = self._get_redis_key(thread_id, checkpoint_id)
        data_to_save = {
            "checkpoint": checkpoint,
            "metadata": metadata,
        }
        
        try:
            serialized_data = self.serde.dumps(data_to_save)
            if self.ttl:
                self.redis_client.setex(key, self.ttl, serialized_data)
            else:
                self.redis_client.set(key, serialized_data)
                
            updated_config = config.copy()
            updated_config['configurable']['checkpoint_id'] = checkpoint_id
            return updated_config
        except Exception as e:
            raise RuntimeError(f"Failed to save checkpoint {checkpoint_id}: {e}")

    def put_writes(self, config: RunnableConfig, writes: Sequence[Tuple[str, Any]], task_id: str) -> None:
        """Saves intermediate writes to Redis."""
        thread_id = config["configurable"]["thread_id"]
        key = self._get_writes_redis_key(thread_id, task_id)
        
        writes_data = {
            "writes": writes,
            "task_id": task_id,
            "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat()
        }
        
        try:
            serialized_data = self.serde.dumps(writes_data)
            if self.ttl:
                self.redis_client.setex(key, self.ttl, serialized_data)
            else:
                self.redis_client.set(key, serialized_data)
        except Exception as e:
            print(f"Warning: Failed to save writes for task {task_id}: {e}")

    def list(self, config: Optional[RunnableConfig], *, filter=None, before=None, limit=None) -> Generator[CheckpointTuple, None, None]:
        """Lists checkpoint tuples for a thread."""
        if not config or "thread_id" not in config.get("configurable", {}):
            raise ValueError("Config with thread_id must be provided")
        
        thread_id = config["configurable"]["thread_id"]
        pattern = self._get_thread_pattern(thread_id)
        keys = self.redis_client.keys(pattern)
        
        # Sort keys by checkpoint_id (timestamp)
        sorted_keys = sorted(keys, key=lambda k: k.decode('utf-8').split(':')[-1], reverse=True)
        
        if limit:
            sorted_keys = sorted_keys[:limit]
        
        count = 0
        for key in sorted_keys:
            if limit and count >= limit:
                break
                
            try:
                checkpoint_data = self.redis_client.get(key)
                if checkpoint_data:
                    saved = self.serde.loads(checkpoint_data)
                    checkpoint_id = key.decode('utf-8').split(':')[-1]
                    
                    chk_config = {"configurable": {"thread_id": thread_id, "checkpoint_id": checkpoint_id}}
                    yield CheckpointTuple(
                        config=chk_config,
                        checkpoint=saved["checkpoint"],
                        metadata=saved.get("metadata"),
                        pending_writes=saved.get("pending_writes", []),
                    )
                    count += 1
            except Exception as e:
                print(f"Error processing checkpoint {key}: {e}")
