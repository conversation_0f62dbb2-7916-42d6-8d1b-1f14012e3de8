import os
from sentence_transformers import SentenceTransformer
import faiss
import pandas as pd



class ColumnRag:
    def __init__(self):
        self.df = None
        base_dir = os.path.dirname(__file__) 
        self.model_name = "all-MiniLM-L6-v2"
        
        self.model = SentenceTransformer(self.model_name)
        self.data_file = os.path.join(base_dir, "assets", "Elyx_New_Views.xlsx")
        self.embeddings_path = os.path.join(base_dir, "assets", "column_embeddings.index")

        self.index = None
        self.embeddings = None
    def _load_model(self):
        embedding_model = SentenceTransformer(self.model_name) 
        return embedding_model
    
    def _load_data(self):
        df_sample = pd.read_excel(self.data_file, engine='openpyxl')
        df_sample = df_sample[df_sample['COLUMN_NAME'] != 'COM_ID']
        df_sample['COLUMN_NAME'] = df_sample['COLUMN_NAME'].str.replace('_', ' ').str.strip().str.casefold()
        df_sample['COLUMN_DESCRIPTION'] = df_sample['COLUMN_DESCRIPTION'].str.strip()
        return df_sample
    
    def build_DB(self):
        print("Loading model...")
        model = self._load_model()
        print("Loading data...")
        data = self._load_data()
        print("Building embeddings...")
        combined_strings = data.apply(
            lambda row: f" {row['COLUMN_NAME']}: {row['COLUMN_DESCRIPTION']}",
            axis=1
        ).to_list()
        embeddings = model.encode(combined_strings, convert_to_numpy=True)
        print("Normalizing embeddings for cosine similarity...")
        faiss.normalize_L2(embeddings)
        self.index = faiss.IndexFlatIP(embeddings.shape[1])
        self.index.add(embeddings)
        print("Saving index to file...")
        faiss.write_index(self.index, self.embeddings_path)
        return "success"


    def build_embeddings(self):
        combined_strings = self.df.apply(
            lambda row: f" {row['COLUMN_NAME']}: {row['COLUMN_DESCRIPTION']}",
            axis=1
        ).to_list()
        self.embeddings = self.model.encode(combined_strings, convert_to_numpy=True)
        
        # Normalize for cosine similarity
        faiss.normalize_L2(self.embeddings)
        
        return self.embeddings
    def load_data(self, path: str):
        """
        Load a new DataFrame into the QA system.
        
        Args:
            path (str): The file path to the CSV file containing the Elyx New Views dataset.
        """
        self.df = pd.read_csv(path, encoding_errors='ignore', encoding='Windows-1252')
        self.df = self.df[self.df['COLUMN_NAME'] != 'COM_ID']
        self.df['COLUMN_NAME'] = self.df['COLUMN_NAME'].str.replace('_', ' ').str.strip().str.casefold()
        self.embeddings = None
        self.index = None
        
    def build_index(self):
        if self.embeddings is None:
            self.build_embeddings()
        dim = self.embeddings.shape[1]
        self.index = faiss.IndexFlatIP(dim)  # Inner Product for cosine similarity
        self.index.add(self.embeddings)

    def save_index(self, path: str):
        if self.index is not None:
            faiss.write_index(self.index, path)
        else:
            raise ValueError("Index not built yet.")

    def load_index(self, path: str):
        self.index = faiss.read_index(path)
        
    def search(self, query: str, k: int = 5, threshold: float = 0.5):
        query_embedding = self.model.encode([query], convert_to_numpy=True)
        faiss.normalize_L2(query_embedding)

        similarities, indices = self.index.search(query_embedding, k)
        results = []
        for i, idx in enumerate(indices[0]):
            sim = similarities[0][i]
            if sim >= threshold:
                result = {
                    "VIEW_NAME": self.df.iloc[idx]['VIEW_NAME'],
                    "COLUMN_NAME": self.df.iloc[idx]['COLUMN_NAME'],
                    "COLUMN_DESCRIPTION": self.df.iloc[idx]['COLUMN_DESCRIPTION'],
                    "Similarity": sim
                }
                print(f"Result {i+1}: column_name: {result['COLUMN_NAME']}, similarity: {result['Similarity']}")
                results.append(result)
        return results
    """def reformulate_query(self, question: str):
        
        # Prepare the prompt to instruct the LLM
        column_names = ", ".join(self.df['COLUMN_NAME'].unique())
        system_content = (
            "You are an expert assistant for rewriting user questions to match column names in a dataset.\n"
            "Given a user question that may include initials or shortcuts, replace them with the correct full column names "
            "from the provided list.\n"
            f"Available columns: {column_names}\n"
            "if you didn't find match from the column names, return <nothing>.\n"
            "any non regular initials or shortcuts should be returned as <nothing>.\n"
            "Return only the reformulated question without any explanation.\n"
            
        )

        messages = [
            SystemMessage(content=system_content),
            HumanMessage(content=f"User question: {question}")
        ]

        reformulated_response = self.llm.invoke(messages)

        # Extract the text from the response object depending on your LLM output format
        if isinstance(reformulated_response, dict) and 'content' in reformulated_response:
            return reformulated_response['content']
        elif hasattr(reformulated_response, 'content'):
            return reformulated_response.content
        else:
            return str(reformulated_response)

    def format_messages(self, question: str, results: list):
        system_content = (
            "You are a helpful assistant that can answer questions about the Elyx New Views dataset. "
            "The dataset contains information about various views and their columns, including descriptions of each column. "
            "You can answer questions about specific columns, views, or general information contained in the dataset. "
            "You should provide accurate and concise answers based on the data provided in the dataset. "
            "If you do not know the answer, you should say that you do not know the answer. "
            "If the question is not related to the dataset, you should say that you cannot answer that question. "
            "You should not make up answers or provide information that is not in the dataset. "
            "If the question is about a specific column, you should provide the COLUMN_DESCRIPTION of that column ONLY. "
            "If the question is about a specific data in column, you should provide ONLY the explanation of data in that column."
            "do not suggest any other information or sources to get the information."
            "wrap the answer in a humanly spoken format."
            "you might get short format for column of initials. predict what the user means by that initials."
        )

        context = "\n".join(
            [f"{r['COLUMN_NAME']}: {r['COLUMN_DESCRIPTION']}" for r in results]
        )

        messages = [
            SystemMessage(content=system_content),
            HumanMessage(content=f"Question: {question}\n\nContext:\n{context}")
        ]

        return messages"""
    
    """def respond(self, question: str, k: int = 5):
        # Step 1: Reformulate the user question
        refined_question = self.reformulate_query(question)
        print(f"Reformulated Question: {refined_question}")
        results = self.search(refined_question, k)
        print(f"Search Results: {results}")
        messages = self.format_messages(refined_question, results)
        response = self.llm.invoke(messages)
        return response

    def get_messages(self, question: str, k: int = 5):
        results = self.search(question, k)
        messages = self.format_messages(question, results)
        return messages"""
    
if __name__ == "__main__":
    # Example usage
    rag = ColumnRag()
    print(rag.build_DB())