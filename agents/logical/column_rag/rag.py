import pandas as pd
import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
from langchain_community.llms import HuggingFacePipeline
from transformers import pipeline
import os

class ColumnsDescriptionRAG:
    def __init__(self):
        base_dir = os.path.dirname(__file__) 
        self.model_name = "all-MiniLM-L6-v2"
        self.data_file = os.path.join(base_dir, "assets", "Elyx_New_Views.xlsx")
        self.embeddings = os.path.join(base_dir, "assets", "column_embeddings.index")
        self.model = self._load_model()
        self.index = self._load_index()
        self.data = self._load_data()

    def _load_model(self):
        embedding_model = SentenceTransformer(self.model_name) 
        return embedding_model
    
    def _load_data(self):
        df_sample = pd.read_excel(self.data_file, engine='openpyxl')
        df_sample = df_sample[df_sample['COLUMN_NAME'] != 'COM_ID']
        df_sample['COLUMN_NAME'] = df_sample['COLUMN_NAME'].str.replace('_', ' ').str.strip().str.casefold()
        df_sample['COLUMN_DESCRIPTION'] = df_sample['COLUMN_DESCRIPTION'].str.strip()
        return df_sample
    

    def _load_index(self):
        index = faiss.read_index(self.embeddings)
        return index
    

    def search(self, query: str, k: int = 5, threshold: float = 0.0):
        query_embedding = self.model.encode([query], convert_to_numpy=True)
        faiss.normalize_L2(query_embedding)

        similarities, indices = self.index.search(query_embedding, k)
        results = []
        for i, idx in enumerate(indices[0]):
            sim = similarities[0][i]
            if sim >= threshold:
                result = {
                    "VIEW_NAME": self.data.iloc[idx]['VIEW_NAME'],
                    "COLUMN_NAME": self.data.iloc[idx]['COLUMN_NAME'],
                    "COLUMN_DESCRIPTION": self.data.iloc[idx]['COLUMN_DESCRIPTION'],
                    "Similarity": sim
                }
                print(f"Result {i+1}: column_name: {result['COLUMN_NAME']}, similarity: {result['Similarity']}")
                results.append(result)
        return results
    def _build_table_dict(self, query, top_k=5):
        result = self.search(query, top_k)
        

        return result

    def _build_prompt_from_dict(self, table_dicts):
        context = "\n".join(
            [f"{r['COLUMN_NAME']}: {r['COLUMN_DESCRIPTION']}\n" for r in table_dicts]
        )
        
        return context
    
    def build_prompt(self, query, top_k=5):
        tables_dict = self._build_table_dict(query, top_k)
        prompt = self._build_prompt_from_dict(tables_dict)
        return prompt

columnsDescriptionRAG = ColumnsDescriptionRAG()