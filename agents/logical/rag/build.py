import pandas as pd
import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
from langchain_community.llms import HuggingFacePipeline
from transformers import pipeline
import os

class RAGBuild:
    def __init__(self):
        base_dir = os.path.dirname(__file__) 
        self.model_name = "all-MiniLM-L6-v2"
        self.data_file = os.path.join(base_dir, "assets", "Elyx_Tables.xlsx")
        self.embeddings = os.path.join(base_dir, "assets", "embeddings.index")

    def _load_model(self):
        embedding_model = SentenceTransformer(self.model_name) 
        return embedding_model
    
    def _load_data(self):
        df_sample = pd.read_excel(self.data_file)
        df_sample = df_sample[df_sample["flag"] != "default"].reset_index()
        return df_sample
    
    def _add_text_col(self, df):
        df["combined_text"] = df.apply(lambda x: f"column name: {x.COLUMN_NAME} | column description: {x.COLUMN_DESCRIPTION}" , axis=1)
        return df
    
    def build_DB(self):
        model = self._load_model()
        data = self._load_data()
        data = self._add_text_col(data)
        embeddings = model.encode(data["combined_text"].tolist(), convert_to_numpy=True)
        index = faiss.IndexFlatL2(embeddings.shape[1]) 
        index.add(embeddings)
        faiss.write_index(index, self.embeddings)
        return "success"


if __name__=="__main__":
    rag = RAGBuild()
    print(rag.build_DB())