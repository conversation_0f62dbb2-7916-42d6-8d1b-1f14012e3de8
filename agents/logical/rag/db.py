import pandas as pd
import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
from langchain_community.llms import HuggingFacePipeline
from transformers import pipeline
import os
import json

class ColumnsRAG:
    def __init__(self):
        base_dir = os.path.dirname(__file__) 
        self.model_name = "all-MiniLM-L6-v2"
        self.data_file = os.path.join(base_dir, "assets", "Elyx_Tables.xlsx")
        self.embeddings = os.path.join(base_dir, "assets", "embeddings.index")
        self.model = self._load_model()
        self.index = self._load_index()
        self.data = self._load_data()
        self.default_data = self._load_default_data()

    def _load_model(self):
        embedding_model = SentenceTransformer(self.model_name) 
        return embedding_model
    
    def _load_data(self):
        df_sample = pd.read_excel(self.data_file)
        df_sample = df_sample[df_sample["flag"] != "default"].reset_index()
        return df_sample

    def _load_default_data(self):
        df_sample = pd.read_excel(self.data_file)
        df_sample = df_sample[df_sample["flag"] == "default"].reset_index()
        return df_sample
    
    def _load_index(self):
        index = faiss.read_index(self.embeddings)
        return index
    
    def _retrieve_tables(self, query, top_k=20):
        query_embedding = self.model.encode([query], convert_to_numpy=True)
        
        _, indices = self.index.search(query_embedding, top_k)
        
        result = self.data.iloc[indices[0]][["TABLE_NAME", "COLUMN_NAME","TABLE_DESCRIPTION", "COLUMN_DESCRIPTION"]]
        return result
    
    def _build_table_dict(self, query, top_k=20):
        df = self._retrieve_tables(query, top_k)
        df = pd.concat([self.default_data , df], axis=0).drop_duplicates().reset_index()
        result = []
        for table_name, group in df.groupby("TABLE_NAME"):
            table_description = group["TABLE_DESCRIPTION"].iloc[0]

            # Start with COM_ID as the first column
            columns = {}

            # Add the actual columns from the dataframe
            table_columns = dict(zip(group["COLUMN_NAME"], group["COLUMN_DESCRIPTION"]))
            columns.update(table_columns)

            result.append({
                "table_name": table_name,
                "table_description": table_description,
                "columns": columns
            })

        return result

    def _build_prompt_from_dict(self, table_dicts):

        prompt = f"{json.dumps(table_dicts, indent=2)}"

        return prompt
    
    def build_prompt(self, query, top_k=20):
        tables_dict = self._build_table_dict(query, top_k)
        prompt = self._build_prompt_from_dict(tables_dict)
        return prompt

columns_rag = ColumnsRAG()