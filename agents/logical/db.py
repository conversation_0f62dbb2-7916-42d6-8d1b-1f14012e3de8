import json
import cx_<PERSON>
import datetime
import pandas as pd
from typing import Dict, Any
from langchain.tools import tool

from config import Config
from models.base import model_backer, ModelBake

from agents.state import BOMState
from agents.logical.rag.db import columns_rag

from tools.system.file_registry import file_registry
from tools.system.s3 import upload_excel_to_s3

c = Config()
#llm = model_backer(ModelBake.LARGE)
# llm = model_backer(ModelBake.NOVA_PRO)

# session pool
from database import DBPool_obj
db_pool = DBPool_obj.db_pool

import time
import logging, os
from logging.handlers import TimedRotatingFileHandler
# Create a folder for logs if it doesn't exist
log_folder = c.logs_folder
if not os.path.exists(log_folder):
    os.makedirs(log_folder)

# Set the log file to be inside the folder
log_file = os.path.join(log_folder, "SQL.log")
# Create a handler that rotates the log file every minute
handler = TimedRotatingFileHandler(log_file, when="D", interval=1, backupCount=60)

# Set the log format to include timestamp, log level, and message
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)

# Create a logger, set level, and add the handler
logger = logging.getLogger("SQLLogger")
logger.setLevel(logging.INFO)
logger.addHandler(handler)

@tool
def get_count_parts_in_bom(bom_id: int):
    """Get the number of parts in a specific BOM, this tool gets the total count, Do Not use this tool to get the unique count"""

    sql_query = f"SELECT COUNT(*) AS Total_Parts_Count FROM CUST_DATA.BOM_RESULT WHERE BOM_ID = {bom_id}"
    connection = cx_Oracle.connect(c.db__connection_string)
    result = pd.read_sql(sql_query, con=connection)
    return result.to_markdown()

tools = [get_count_parts_in_bom]

#llm = llm.bind_tools(tools)

class QueryTimeoutError(Exception):
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message
        super().__init__(f"[{code}] {message}")

class OracleSQLAgent:
    """
    An agent that translates natural language questions into Oracle SQL queries,
    executes them, and returns results. It includes retry logic for failed queries.
    """
    
    def __init__(
        self,
        oracle_connection_string: str = c.db__connection_string,
        model_id: str = "us.anthropic.claude-3-7-sonnet-20250219-v1:0", #"us.amazon.nova-pro-v1:0",
        region: str = "us-west-2",
        max_retries: int = 3
    ):
        """
        Initialize the Oracle SQL Agent.
        """
        #self.model = model_backer(ModelBake.NOVA_PRO)
        self.model = model_backer(ModelBake.CLAUDE)
        self.connection_string = oracle_connection_string
        print(self.connection_string)
        self.max_retries = max_retries
        self.schema_info = None
    
    def get_tables_context(self, query, top_k=20):
        rag = columns_rag
        prompt_context = rag.build_prompt(query, top_k)
        return prompt_context

    def connect(self) -> cx_Oracle.Connection:
        """Establish a connection to the Oracle database."""
        try:
            connection = db_pool.acquire()
            connection.callTimeout = 240000  # 4 minute (in milliseconds)
            c.logger(__name__).info("> Connected to DB")
            return connection
        except cx_Oracle.Error as error:
            c.logger(__name__).info("> Failed to connect to DB")
            raise ConnectionError(f"Failed to connect to Oracle: {error}")

    def generate_sql_query(self, question: str, bom_id: int, prompt: str) -> str:
        """
        Generate an Oracle SQL query from a natural language question using Claude.
        
        Args:
            question: The natural language question to translate into SQL
            bom_id: The id of the bom
            prompt: The sql agent prompt
        Returns:
            str: The generated SQL query
        """

        # Build the prompt for llm
        # tables_context = self.get_tables_context(question)
        # prompt = prompt.format(bom_id, tables_context, question)

        messages = [
            (
                "system",
                "Generate only the Oracle SQL query with no explanations or additional text.",
            ),
            ("human", prompt),
        ]
        c.logger(__name__).info(f"SQL> {messages}")
        response = self.model.invoke(messages)
        # if response.response_metadata['stopReason'] == 'tool_use':
        #     tool_call = response.tool_calls[0]
        #     tool_name = tool_call['name']
        #     tool_args = tool_call['args']
        #     result = get_count_parts_in_bom.invoke(tool_args)
        #     return result

        # Extract just the SQL query from the response
        sql_query = response.content
        sql_query = sql_query.replace(";","")
        c.logger(__name__).info(f"> clean SQL: {sql_query}")
        # Clean the response to remove any code blocks or explanations
        if sql_query.startswith("```sql"):
            sql_query = sql_query.split("```sql")[1].split("```")[0].strip()
        elif sql_query.startswith("```"):
            sql_query = sql_query.split("```")[1].split("```")[0].strip()
            
        c.logger(__name__).info(f"> SQL: {sql_query}")

        # check bom id
        if 'ELYX.BOM_RESULT_DATA' in sql_query and 'BOM_ID =' not in sql_query:
            return "select [you must add filter on bom id with BOM_RESULT_DATA don't forget]"

        return sql_query

    def execute_query(self, sql_query: str):
        c.logger(__name__).info("DB> Start Connection")
        connection = self.connect()
        try:
            c.logger(__name__).info("DB> Start Execute")
            try:
                result = pd.read_sql(sql_query, con=connection)
                c.logger(__name__).info("DB> Finish Execute")
                return result
            except cx_Oracle.DatabaseError as e:
                if "DPI-1067" in str(e) or "ORA-01013" in str(e):
                    c.logger(__name__).error("> Query execution exceeded 4 minutes (Oracle timeout).")
                    raise QueryTimeoutError(code=408, message="Query execution exceeded 4 minutes.")
                raise
        finally:
            db_pool.release(connection)
            c.logger(__name__).info("DB> Connection released")


    def handle_query_error(self, error: Exception, question: str, bom_id: int, failed_query: str, attempt: int, prompt: str) -> str:
        """
        Generate a corrected query based on the error message and previous query.
        
        Args:
            error: The exception raised during query execution
            question: Original natural language question
            failed_query: The query that caused the error
            attempt: Current attempt number
            prompt: sql agent prompt
        Returns:
            str: A corrected SQL query
        """

        # tables_context = self.get_tables_context(question)
        # prompt = prompt.format(bom_id, tables_context, question)

        prompt += f"""
        ------------------
        Failed Query:
        {failed_query}
        ------------------
        Error Message:
        {str(error)}
        ------------------
        Generate ONLY the corrected Oracle SQL query:
        """
        
        messages = [
            (
                "system",
                "Generate only the Oracle SQL query with no explanations or additional text.",
            ),
            ("human", prompt)
        ]

        response = self.model.invoke(messages)
        
        # Extract just the SQL query from the response
        corrected_query = response.content

        print(f"corrected_query: {corrected_query}")
        
        # Clean the response to remove any code blocks or explanations
        corrected_query = corrected_query.replace(";","")
        if corrected_query.startswith("```sql"):
            corrected_query = corrected_query.split("```sql")[1].split("```")[0].strip()
        elif corrected_query.startswith("```"):
            corrected_query = corrected_query.split("```")[1].split("```")[0].strip()
            
        return corrected_query

    def ask(self, question: str, bom_id: int, prompt: str, conversation_id: str) -> Dict[str, Any]:
        query = self.generate_sql_query(question, bom_id, prompt)

        if "select" not in query.lower():
            return {
                "success": False,
                "error": query}

        for attempt in range(self.max_retries):
            try:
                start_time = time.time()
                results = self.execute_query(query)
                execution_time = time.time() - start_time

                logger.info(f"conversation id: {conversation_id}")
                logger.info(f"SUCCESS in {execution_time:.2f}s: {query}")
                c.logger(__name__).info(f"> SQL Result: {results}")

                if len(results) == 0:
                    return {"success": True, "error": "no data found"}

                results = results.applymap(
                    lambda x: "Unknown" if isinstance(x, (int, float)) and x < 0 else x
                )

                if len(results) > 10:
                    current_timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    file_title = f"data_{current_timestamp}.xlsx"
                    s3_file_name = f"{c.downloaded__s3_path}/{file_title}"
                    url = upload_excel_to_s3(results, c.downloaded__s3_bucket, s3_file_name)
                    file_registry.add_file(conversation_id, url, file_title)
                    column_count = len(results.columns)
                    full_result = "attached report"
                    if column_count>15:
                        results = results.iloc[0:10,0:15]
                        full_result = "this is the first 15 columns but the rest of the required columns data exist in the attached report"
                    return {
                        "success": True,
                        "top records results": results.head(10).fillna("Not Found").to_dict(),
                        "full results": full_result,
                    }

                return {"success": True, "results": results.fillna("Not Found").to_dict()}

            except QueryTimeoutError as e:
                logger.error(f"TIMEOUT: {query}")
                logger.error(f"[{e.code}] {e.message}")
                c.logger(__name__).error(f"[{e.code}] {e.message}")
                raise  # propagate up with code & message

            except Exception as e:
                logger.error(f"FAILED: {query}")
                logger.error(f"Error: {str(e)}")
                c.logger(__name__).error(e)

                if attempt < self.max_retries - 1:
                    query = self.handle_query_error(e, question, bom_id, query, attempt + 1, prompt)
                else:
                    return {
                        "success": False,
                        "error": "Error fetching Data"}


                    