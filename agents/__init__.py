# Package Imports
import json
import logging
from functools import partial


from langchain_aws import ChatBedrockConverse
from langgraph.graph import StateGraph, END, START
from langgraph.checkpoint.memory import MemorySaver
from memory.checkpointer.redis import RedisCheckpointer
from memory.checkpointer.s3 import S3<PERSON>heckpointer
from memory.checkpointer.file import get_chat_history_local_file
# from langfuse.callback import CallbackHandler
# from langgraph.checkpoint.redis import RedisSaver
REDIS_URI = "redis://localhost:6379"
# checkpointer = RedisSaver.from_conn_string(REDIS_URI)


# Internal Imports
from agents.state import BOMState, init_state
from config import Config

## Refactor Nodes
from agents.nodes.refactor import (
    initialize_question_state, safe_question_refactor,
    safe_generate_steps, question_planner_node
)
## Follow-up Nodes
from agents.nodes.followup import (
    follow_up_check, safe_should_process_followup,
    follow_up_process
)
## BOM Nodes
from agents.nodes.bom import (
    check_requires_data, general_question_check, run_rag_agent, run_oracle_agent_cost,
    run_oracle_agent_other, safe_should_use_bom_data, run_enhanced_answering_agent,
    run_enhanced_rag_agent, ask_user_for_clarification, 
    rag_collector_node, check_columns_clearance, check_cost_savings, check_cost_saving_need
)

from agents.nodes.general import (
    run_general_answering_agent
)
from agents.nodes.guard import (
    guardrail_check, guardrail_check_question_scope
)

from dotenv import load_dotenv

load_dotenv(override=True)


c = Config()
# checkpointer = RedisCheckpointer(REDIS_URI)
if c.checkpointer__type == "S3":
    c.logger(__name__).info("main> You choose S3...")
    checkpointer = S3Checkpointer(
        bucket_name=c.checkpointer__s3_bucket,
        region=c.checkpointer__s3_region
    )
else:
    checkpointer = MemorySaver()

# langfuse_handler = CallbackHandler(
#     secret_key=c.langfuse__secret_key,
#     public_key=c.langfuse__public_key,
#     host=c.langfuse__host
# )

def  create_fixed_enhanced_workflow():
    """
    Create enhanced BOM workflow with proper error handling and key management
    """
    workflow = StateGraph(BOMState)
    
    ###################
    ## NODE ADDITION ##
    ###################
    
    # Add initialization node
    # This node is responsible of creating the question state and putting the 
    # initial values for next steps
    workflow.add_node("initialize_question_state", initialize_question_state)
    
    # Add guardrail node
    # This node will contains the guardrail checks + the topic deviation
    # check guardrail config and model deviation to custom the security for the input question
    workflow.add_node("guardrail_check_question_scope", guardrail_check_question_scope)
    
    # Add follow node
    # Followup can be divided into 2 nodes, the check stated below and the logic
    # The follow up check is the flag check to tell if the question is a followup or not
    workflow.add_node("follow_up_check", partial(follow_up_check, checkpointer=checkpointer))
    
    # follow up logic node, which responsible for building the checks for the followup
    workflow.add_node(
        "follow_up_process",
        partial(follow_up_process, checkpointer=checkpointer)
    )
    
    # check if general question or requires data
    workflow.add_node("check_requires_data_or_general", check_requires_data)
    # workflow.add_node("safe_question_refactor", safe_question_refactor)
    # workflow.add_node("safe_generate_steps", safe_generate_steps)
    workflow.add_node("ask_user_for_clarification", ask_user_for_clarification)
    
    
    #workflow.add_node("rag_collector_node", rag_collector_node)
    workflow.add_node("question_planner_node", question_planner_node)
    
    #workflow.add_node("run_enhanced_rag_agent", run_enhanced_rag_agent)
    workflow.add_node("check_cost_savings", check_cost_savings)
    workflow.add_node("run_oracle_agent_cost", run_oracle_agent_cost)
    workflow.add_node("run_oracle_agent_other", run_oracle_agent_other)
    workflow.add_node("run_enhanced_answering_agent", run_enhanced_answering_agent)
    workflow.add_node("run_general_answering_agent", run_general_answering_agent)
    
    # Define workflow with proper initialization
    
    # fill init state
    workflow.add_edge(START, "initialize_question_state")
    # check if question is secured or not
    workflow.add_edge("initialize_question_state", "guardrail_check_question_scope")
    # workflow.add_edge("initialize_question_state", "follow_up_check")
    # guardrail check
    workflow.add_conditional_edges(
        "guardrail_check_question_scope",
        guardrail_check,
        {
            "in-scope": "follow_up_check",
            "out-scope": END
        }
    )

    # workflow.add_edge("guardrail_check_question_scope", "follow_up_check")


    # General Question check
    workflow.add_conditional_edges(
        "check_requires_data_or_general",
        general_question_check,
        {
            "needs_data": "question_planner_node",
            "general_question": "run_general_answering_agent"
        }
    )
    # Follow-up routing
    #check if the question is general, followup, or data 
    workflow.add_conditional_edges(
        "follow_up_check",
        safe_should_process_followup,
        {
            "followup": "follow_up_process",        # if followup
            "continue": "check_requires_data_or_general"    # if data
                                                    # if general
        }
    )
    workflow.add_edge("follow_up_process", "check_requires_data_or_general")
    # need to add rag_collector_node
    # workflow.add_edge("follow_up_process", "rag_collector_node")
    
    # need to add question_planner_node
    # big brain node
    # workflow.add_edge("rag_collector_node", "question_planner_node")
    # workflow.add_edge("question_planner_node", END)
    
    # Check if any column need clarification
    workflow.add_conditional_edges(
        "question_planner_node",
        check_columns_clearance,
        {
            "clear": "check_cost_savings",
            "clarify": "ask_user_for_clarification"
        }
    )

    workflow.add_conditional_edges(
        "check_cost_savings",
        check_cost_saving_need,
        {
            "cost_saving": "run_oracle_agent_cost",
            "other": "run_oracle_agent_other"
        }
    )
    
    
    
    # need to check if columns clear
    
    # Question refactoring flow
    # workflow.add_edge("safe_question_refactor", "safe_generate_steps")
    # workflow.add_edge("safe_generate_steps", "safe_recommend_columns")
    workflow.add_edge("ask_user_for_clarification", END)
    
    # Data processing routing
    # workflow.add_conditional_edges(
    #     "check_requires_data",
    #     safe_should_use_bom_data,
    #     {
    #         "bom_query": "run_enhanced_oracle_agent",
    #         "general_query": "run_general_answering_agent"
    #     }
    # )
    
    # # Enhanced processing chain
    # workflow.add_edge("run_enhanced_rag_agent", "run_enhanced_oracle_agent")
    workflow.add_edge("run_oracle_agent_cost", "run_enhanced_answering_agent")
    workflow.add_edge("run_oracle_agent_other", "run_enhanced_answering_agent")
    
    # # # End points
    workflow.add_edge("run_enhanced_answering_agent", END)
    workflow.add_edge("run_general_answering_agent", END)
    
    # Compile with memory
    # memory = MemorySaver()
    app = workflow.compile(checkpointer=checkpointer)
    
    return app

def run_fixed_enhanced_bom_query(app, question: str, session_id: str = "default"):
    """
    Execute enhanced BOM query with proper error handling, state initialization, and interrupt support.
    """
    # Proper initial state with all required keys
    initial_state = init_state(question, session_id)
    config = {
        "configurable": {"thread_id": session_id},
        #"callbacks": [langfuse_handler]
    }
    
    # Run the app once
    result = app.invoke(initial_state, config)
    
    # Check for interrupt
    if isinstance(result, dict) and result.get("type") == "interrupt":
        # Print the message and recommended columns (if any)
        print("Interrupt detected. Please respond to the following:")
        print(result.get("message", "No message provided."))
        print("Recommended columns:", result.get("recommended_columns", []))
        
        # Prompt for user input
        user_input = input("Your response: ")
        
        # Update the state with the user's input.
        # This assumes your app can handle a state update with user_input.
        # If your app expects a specific key, update accordingly.
        # For example, you might add user_input to the state or config.
        # Here, we add it to the state for illustration.
        # (Adjust as needed for your app's design.)
        initial_state["user_response"] = user_input
        
        # Resume the app with the updated state
        result = app.invoke(initial_state, config)
    
    # Update chat history (if not interrupted, or after resume)
    chat_history = get_chat_history_local_file(session_id, conversation_dir=c.conversations_folder)
    chat_history.add_user_message(result.get("original_question"))
    chat_history.add_ai_message(result.get("final_answer"))
    
    # Return the result
    return {
        "answer": result.get("final_answer", "Unable to process question"),
        "original_question": result.get("original_question"),
        "refactored_question": result.get("refactored_question"),
        "answering_steps": result.get("answering_steps", []),
        "recommended_columns": result.get("recommended_columns", []),
        "confidence_score": result.get("confidence_score", 0.0),
        "complexity": result.get("question_complexity", "unknown"),
        "current_step": result.get("current_step", "completed"),
        "state": result
    }


if __name__ == "__main__":
    test_questions = [
        "Show me data sample with price and lead time information",
        "How many parts are in the BOM?",
        "What is the total cost of components?"
    ]
    
    for question in test_questions:
        print(f"\nTesting: {question}")
        result = run_fixed_enhanced_bom_query(question)
        
        if "error" in result:
            print(f"Error: {result['error']}")
        else:
            print(f"Answer: {result['answer']}")
            print(f"Steps: {result['answering_steps']}")
        print("-" * 80)
