from typing import TypedDict, List, Optional, Literal, Dict

# Enhanced state definition with refactoring capabilities
class BOMState(TypedDict):
    """Enhanced state definition including question refactoring components"""
    final_answer: str
    original_question: str

    # Required Info
    conversation_id: str
    bom_id: int
    
    # Guardrail
    in_scope: bool = True
    
    # Original question processing
    question: str
    conversation_history: List[dict]
    conversation_summary: str
    
    # Question refactoring outputs
    refactored_question: str
    clarification_message: str
    needs_clarification: bool
    
    # Original state variables
    is_followup: bool
    columns_required: bool
    columns_clear: bool
    available_columns: List[str]
    selected_columns: List[str]
    requires_data: bool
    current_step: str
    user_confirmation: Optional[bool]
    
    # Data content
    bom_data: Optional[dict] = ""
    oracle_results: Optional[str] = ""
    sql_query: Optional[str] = ""
    cost_saving: Optional[str] = ""
    
def init_state(
    question: str,
    bom_id: int,
    session_id: str = "default"
) -> BOMState:
    return {
        "original_question": question,
        "question": question,  # Initialize both keys
        "refactored_question": "",
        "conversation_id": session_id,
        "bom_id": bom_id,
        "conversation_history": [],
        "conversation_summary": "",
        "answering_steps": [],
        "step_descriptions": [],
        "recommended_columns": [],
        "confidence_score": 0.0,
        "question_complexity": "unknown",
        "requires_decomposition": False,
        "domain_context": {},
        "is_followup": False,
        "columns_required": False,
        "columns_clear": False,
        "available_columns": [],
        "selected_columns": [],
        "requires_data": True,
        "bom_data": None,
        "oracle_results": None,
        "current_step": "start",
        "user_confirmation": None,
        "cost_saving": "",
        "final_answer": "",
        "clarification_message": "",
        "needs_clarification": False,
        "in_scope": True
    }