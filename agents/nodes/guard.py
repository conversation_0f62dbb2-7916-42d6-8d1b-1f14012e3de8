import json
import boto3
import asyncio
from typing import Literal
from datetime import datetime

from agents.state import BOMState
from config import Config
from models.base import model_backer, instructor_baker, ModelBake

from structures.guard import GuardModelOutput
from prompts.guard import GUARDRAIL_CHECK_PROMPT

c = Config()
# llm = model_backer(ModelBake.NOVA_LITE)


def guardrail_check_question_scope(state: BOMState) -> BOMState:
    """
    Guardrail node to check if the question is in scope using LLM.
    Input: state dict with 'question' key.
    Output: state dict updated with 'in_scope' boolean and 'scope_reason'.
    """
    question = state.get('question', '')
    guard_output = asyncio.run(_guardrail_run(question))
    if not guard_output:
        state['in_scope'] = False
        state['scope_reason'] = "guardrail block"
        state['final_answer'] = "Sorry, this question is flagged by guardrail, please stay in the context."
    prompt = GUARDRAIL_CHECK_PROMPT.format(question)
    messages = [{"role": "user", "content": prompt}]
    response = instructor_baker(ModelBake.NOVA_PRO, messages, GuardModelOutput)
    try:
        c.logger(__name__).info(f"guard> {response}")
        if response.in_scope: # and guard_output: # Force check both
            state['in_scope'] = True
        else:
            state['in_scope'] = False
            state['final_answer'] = response.response
        state['scope_reason'] = response.reason
    except Exception as e:
        c.logger(__name__).error(f"guard error> {e}")
        # Fallback: Only check guardrail
        if guard_output:
            state['in_scope'] = True
            state['scope_reason'] = "Question contains BOM-related keywords."
        else:
            state['in_scope'] = False
            state['scope_reason'] = "Question does not relate to BOM domain."
    state['current_step'] = 'guardrail_check_complete'
    return state


async def _guardrail_run(question: str):
    # client = boto3.client(
    #     c.guardrail__runtime,
    #     region_name=c.guardrail__region
    # )
    # response = client.apply_guardrail(
    #     guardrailIdentifier=c.guardrail__id,
    #     guardrailVersion=c.guardrail__version,
    #     source='INPUT',  # or 'OUTPUT' if evaluating model output
    #     content=[{"text": {"text": question}}]
    # )
    
    # Run guardrails in parallel
    tasks = [_apply_single_guardrail(g, question) for g in c.guardrail__configs]
    results = await asyncio.gather(*tasks)
    
    summary = {
        "timestamp": datetime.utcnow().isoformat(),
        "total_guardrails": len(results),
        "blocked_count": sum(1 for r in results if r.get("blocked")),
        "guardrail_results": results,
        "user_input": question
    }
    
    c.logger(__name__).info(f"Multi-Guardrail Check: {summary}")
    
    # Check if all responses are NOT blocked
    return all(response["blocked"] != "GUARDRAIL_INTERVENED" for response in results)

async def _apply_single_guardrail(guardrail_config, question):
    try:
        client = boto3.client(
            c.guardrail__runtime,
            region_name=c.guardrail__region
        )
        response = client.apply_guardrail(
            guardrailIdentifier=guardrail_config["id"],
            guardrailVersion=guardrail_config["version"],
            source="INPUT",
            content=[{"text": {"text": question}}]
        )
        return {
            "guardrail": guardrail_config["id"],
            "blocked": response["action"] == "GUARDRAIL_INTERVENED",
            "response": response
        }
    except Exception as e:
        return {
            "guardrail": guardrail_config["id"],
            "blocked": False,
            "error": str(e)
        }


def guardrail_check(state: BOMState) -> Literal["in-scope", "out-scope"]:
    """Safely check for follow-up with proper key validation"""
    try:
        in_scope = state.get("in_scope", False)
        c.logger(__name__).info(f"guard> {in_scope} XX {state}")
        return "in-scope" if in_scope else "out-scope"
    except Exception as e:
        c.logger(__name__).error(f"Error in follow-up routing: {e}")
        return "continue"
