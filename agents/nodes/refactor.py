import json

from agents.state import B<PERSON><PERSON>tate
from config import Config
from models.base import model_backer, instructor_baker, ModelB<PERSON>
from memory.checkpointer.file import get_chat_history_local_file
from structures.step import Step
from structures.data_requirements import DataRequirement
from structures.planning import PlanningOutput
from prompts.refactor import (
    QUESTION_PLANNER_PROMPT, SAFE_REFACTOR_PROMPT
)


c = Config()
#llm = model_backer(ModelBake.SMALL)

def initialize_question_state(state: BOMState) -> BOMState:
    """
    Initialize question state to ensure both original_question and question exist
    """
    # Ensure question key exists and is properly initialized
    if "question" not in state or not state["question"]:
        state["question"] = state.get("original_question", "")
    
    # Initialize other required keys with defaults
    if "answering_steps" not in state:
        state["answering_steps"] = []
    if "step_descriptions" not in state:
        state["step_descriptions"] = []
    if "recommended_columns" not in state:
        state["recommended_columns"] = []
    if "confidence_score" not in state:
        state["confidence_score"] = 0.0
    if "question_complexity" not in state:
        state["question_complexity"] = "unknown"
    if "requires_decomposition" not in state:
        state["requires_decomposition"] = False
    if "domain_context" not in state:
        state["domain_context"] = {}
    
    state["current_step"] = "question_initialized"
    c.logger(__name__).info(f"Question state initialized: {state['question']}")
    
    return state



def question_planner_node(state: BOMState) -> BOMState:
    """
    Planning for all the question graph next steps
    """
    # Ensure we have a question to work with
    try:    
        original_question = state.get("original_question", "")
        current_question = state.get("question", original_question)
        bom_id = state.get("bom_id")
        
        # if not current_question:
        #     c.logger(__name__).error("No question found in state")
        #     state["refactored_question"] = "No question provided"
        #     state["question"] = "No question provided"
        #     state["current_step"] = "refactor_error"
        #     return state
        
        # conversation_history = state.get("conversation_history", [])
        # chat_history = get_chat_history_local_file("he003", conversation_dir=c.conversations_folder)
        # conversation_history = "\n".join([
        #     f"Q: {m.content}" if m.type == "human" else f"A: {m.content}"
        #     for m in chat_history.messages
        # ])
        conversation_history = ""
        conversation_summary = state.get("conversation_summary", "")
        
        prompt = QUESTION_PLANNER_PROMPT.format(
            current_question, conversation_history, conversation_summary
        )
        
        messages = [{"role": "user", "content": prompt}]
        analysis = instructor_baker(ModelBake.LARGE, messages, PlanningOutput)
        c.logger(__name__).info(f"> Planner: {analysis}")
        
        # Update state with refactored information
        state["refactored_question"] = analysis.refactored_question
        state["question"] = state["refactored_question"]  # Update main question key
        state["clarification_message"] = analysis.clarification_message
        state["needs_clarification"] = analysis.needs_clarification
        
        c.logger(__name__).info(f"Question successfully refactored: {state['refactored_question']}")
        
    except Exception as e:
        c.logger(__name__).error(f"Error in question planning: {e}")
        # Fallback: use original question
        original_question = state.get("original_question", "")
        state["refactored_question"] = original_question
        state["question"] = state["refactored_question"]
        state["clarification_message"] = ""
        state["needs_clarification"] = False
    
    return state


###############################
## Multi-Step Refactor Nodes ##
###############################

def safe_question_refactor(state: BOMState) -> BOMState:
    """
    Safely refactor question with proper error handling and key management
    """
    try:
        # Ensure we have a question to work with
        original_question = state.get("original_question", "")
        current_question = state.get("question", original_question)
        
        if not current_question:
            c.logger(__name__).error("No question found in state")
            state["refactored_question"] = "No question provided"
            state["question"] = "No question provided"
            state["current_step"] = "refactor_error"
            return state
        
        conversation_history = state.get("conversation_history", [])
        
        # Build context from conversation history
        context_summary = ""
        if conversation_history:
            context_items = [
                f"Q: {item.get('question', '')} A: {item.get('answer', '')[:100]}..." 
                for item in conversation_history[-3:]
            ]
            context_summary = "\n".join(context_items)
        
        prompt = SAFE_REFACTOR_PROMPT.format(
            current_question, context_summary
        )

        llm = model_backer(ModelBake.SMALL)
        
        response = llm.invoke([{"role": "user", "content": prompt}])
        analysis = json.loads(str(response.content).replace("```json", "").replace("```", ""))
        
        # Update state with refactored information
        state["refactored_question"] = analysis.get("refactored_question", current_question)
        state["question"] = state["refactored_question"]  # Update main question key
        state["confidence_score"] = analysis.get("confidence_score", 0.5)
        state["question_complexity"] = analysis.get("complexity", "moderate")

        state["current_step"] = "question_planned"
    except Exception as e:
        c.logger(__name__).error(response.content)
        c.logger(__name__).error(f"Error in question refactoring: {e}")
        # Fallback: use original question
        original_question = state.get("original_question", "")
        state["refactored_question"] = original_question
        state["question"] = original_question
        state["confidence_score"] = 0.3
        state["question_complexity"] = "unknown"
        state["requires_decomposition"] = False
        state["current_step"] = "refactor_error_handled"
    
    return state

def safe_generate_steps(state: BOMState) -> BOMState:
    """
    Safely generate answering steps with proper error handling
    """
    try:
        # Ensure we have a question to work with
        question = state.get("question", state.get("original_question", ""))
        if not question:
            c.logger(__name__).error("No question available for step generation")
            state["answering_steps"] = ["Error: No question provided"]
            state["step_descriptions"] = ["Unable to generate steps without a question"]
            state["current_step"] = "steps_error"
            return state
        
        complexity = state.get("question_complexity", "moderate")
        requires_decomposition = state.get("requires_decomposition", False)
        
        prompt = f"""
        Generate detailed answering steps for this BOM analysis question.
        
        Question: {question}
        Complexity: {complexity}
        Requires Decomposition: {requires_decomposition}
        
        Create systematic steps including:
        1. Data identification requirements
        2. Processing methodology
        3. Analysis approach
        4. Response formatting
        
        Format as JSON:
        {{
            "steps": [
                {{
                    "step_number": 1,
                    "action": "Identify required BOM columns",
                    "description": "Determine specific data fields needed",
                    "data_requirements": ["part_number", "quantity"]
                }}
            ],
            "total_steps": 4
        }}
        """
        llm = model_backer(ModelBake.SMALL)
        response = llm.invoke([{"role": "user", "content": prompt}])
        steps_data = json.loads(str(response.content).replace("```json", "").replace("```", ""))
        
        steps = steps_data.get("steps", [])
        state["answering_steps"] = [step.get("action", f"Step {i+1}") for i, step in enumerate(steps)]
        state["step_descriptions"] = [step.get("description", "") for step in steps]
        
        # Extract data requirements
        all_requirements = []
        for step in steps:
            requirements = step.get("data_requirements", [])
            all_requirements.extend(requirements)
        
        if "domain_context" not in state:
            state["domain_context"] = {}
        
        state["domain_context"]["data_requirements"] = list(set(all_requirements))
        state["domain_context"]["total_steps"] = len(steps)
        state["current_step"] = "steps_generated"
        
        c.logger(__name__).info(f"Generated {len(steps)} answering steps")
        c.logger(__name__).info(f"Steps are: {steps}")
        
    except Exception as e:
        c.logger(__name__).error(response.content)
        c.logger(__name__).error(f"Error generating steps: {e}")
        # Provide fallback steps
        state["answering_steps"] = [
            "Analyze question requirements", 
            "Retrieve relevant BOM data", 
            "Process and analyze data",
            "Generate comprehensive response"
        ]
        state["step_descriptions"] = [
            "Understand what the question is asking",
            "Get necessary data from BOM database",
            "Analyze the data according to requirements",
            "Format and present the answer"
        ]
        state["current_step"] = "steps_error_handled"
    
    return state