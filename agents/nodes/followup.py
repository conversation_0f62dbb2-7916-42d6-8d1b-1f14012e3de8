from typing import Literal
import time

from agents.state import B<PERSON><PERSON>tate
from config import Config
from models.base import instructor_baker, model_backer, ModelBake
from memory.checkpointer.file import get_chat_history_local_file

from prompts.followup import (
    FOLLOW_UP_PROCESS_PROMPT, FOLLOW_UP_CHECK_PROMPT
)
from structures.followup import FollowUpModelOutput

c = Config()
# llm = model_backer(ModelBake.SMALL)

def follow_up_check(state: BOMState, checkpointer) -> BOMState:
    """
    Determines if the current question is a follow-up to previous conversation
    """

    # get conversation history
    try:
        checkpoints = list(checkpointer.list({"configurable": {"thread_id": state["conversation_id"]}}, limit=20))
        checkpoints = checkpoints[::-1]
        history_items = []
        for cp in checkpoints:
            c.logger(__name__).info(f"followup> *** checkpointer data ***")
            #c.logger(__name__).info(f"followup> checkpointer data {cp}")
            # channel_values = cp.checkpoint.get('channel_values', {}).get('__start__', {})
            channel_values = cp.checkpoint.get('channel_values', {})
            question = channel_values.get('question', '')
            answer = channel_values.get('final_answer', '')
            #bom_data = channel_values.get('bom_data', '')
            if question and answer:
                history_items.extend([f"Q: {question}", f"A: {answer}"])
        # drop duplicates to take unique msgs
        history_items = list(dict.fromkeys(history_items))
        history_items = "\n".join(history_items)
        
        conversation_context = history_items
    except:
        conversation_context = "\n".join([
            f"Q: {item.get('question', '')} A: {item.get('answer', '')}" 
            for item in state.get('conversation_history', [])
        ])
    
    prompt = FOLLOW_UP_CHECK_PROMPT.format(
        conversation_context, state['question']
    )
    messages = [{"role": "user", "content": prompt}]
    
    try:
        response = instructor_baker(ModelBake.NOVA_PRO, messages, FollowUpModelOutput)
        # response = llm.invoke([{"role": "user", "content": prompt}])
        if response.followup:
            is_followup = True
        else:
            is_followup = False
        state["is_followup"] = is_followup
        state["current_step"] = "followup_check_complete"
        
        c.logger(__name__).info(f"Follow-up check: {is_followup}")
        
    except Exception as e:
        c.logger(__name__).error(f"Error in follow-up check: {e}")
        state["is_followup"] = False
        state["current_step"] = "error"
    
    return state


def follow_up_process(state: BOMState, checkpointer) -> BOMState:
    """
    Detects if the question is a follow-up and rewrites it as a self-contained question using conversation history.
    Updates state['question'] with the clarified version for downstream nodes.
    """
    # 1. Gather full conversation history from the checkpointer
    history_items = []
    chat_history = get_chat_history_local_file(state["conversation_id"], conversation_dir=c.conversations_folder)
    try:
        c.logger(__name__).info("followup> checkpointer data started...")
        t1 = time.time()
        checkpoints = list(checkpointer.list({"configurable": {"thread_id": state["conversation_id"]}}, limit=20))
        t2 = time.time()
        c.logger(__name__).info(f"time: {t2 - t1:.4f} seconds")
        checkpoints = checkpoints[::-1]
        for cp in checkpoints:
            c.logger(__name__).info(f"followup> *** checkpointer data ***")
            #c.logger(__name__).info(f"followup> checkpointer data {cp}")
            # channel_values = cp.checkpoint.get('channel_values', {}).get('__start__', {})
            channel_values = cp.checkpoint.get('channel_values', {})
            question = channel_values.get('question', '')
            answer = channel_values.get('final_answer', '')
            #bom_data = channel_values.get('bom_data', '')
            if question and answer:
                history_items.extend([f"Q: {question}", f"A: {answer}"])
        # drop duplicates to take unique msgs
        history_items = list(dict.fromkeys(history_items))
        history_items = "\n".join(history_items)
    except Exception as e:
        c.logger(__name__).error(f"followup> Couldn't load checkpointer data {e}")
        history_items = "\n".join([
            f"Q: {m.content}" if m.type == "human" else f"A: {m.content}"
            for m in chat_history.messages
        ])

    # 2. Format conversation history for the LLM
    if history_items == []:
        conversation_context = "\n".join([
            f"Q: {m.content}" if m.type == "human" else f"A: {m.content}"
            for m in chat_history.messages
        ])
    else:
        conversation_context = history_items
    
    c.logger(__name__).info(conversation_context)
    # 3. Prompt LLM to rewrite the question with context
    llm = model_backer(ModelBake.SMALL)
    prompt = FOLLOW_UP_PROCESS_PROMPT.format(
        conversation_context, state['question']
    )

    try:
        response = llm.invoke([{"role": "user", "content": prompt}])
        refactored_question = response.content.strip()
        # TODO Followup should return the last five questions
        state["conversation_history"] = [history_items]
        # TODO Need to adjust the above model to be instructor returning (refactor question, summary)
        state["conversation_summary"] = conversation_context
        state["question"] = refactored_question  # <-- This is crucial for downstream nodes!
        state["is_followup"] = True
        state["current_step"] = "followup_check_complete"
    except Exception:
        # If LLM fails, keep original question
        state["is_followup"] = False
        state["current_step"] = "error"

    return state

def safe_should_process_followup(state: BOMState) -> Literal["followup", "continue"]:
    """Safely check for follow-up with proper key validation"""
    try:
        is_followup = state.get("is_followup", False)
        return "followup" if is_followup else "continue"
    except Exception as e:
        c.logger(__name__).error(f"Error in follow-up routing: {e}")
        return "continue"


def run_general_answering_agent(state: BOMState) -> BOMState:
    """
    Handles general BOM questions that don't require specific data
    """
    question = state["question"]
    llm = model_backer(ModelBake.SMALL)
    prompt = f"""
    Question: {question}
    
    This is a general question about Bills of Materials (BOMs) that doesn't require 
    specific data retrieval. Provide a comprehensive answer that covers:
    
    1. Direct response to the question
    2. Relevant BOM concepts and best practices
    3. Practical guidance or examples
    4. Industry standards or common approaches
    5. Any important considerations or caveats
    
    Make the response informative and actionable for someone working with BOMs.
    """
    
    try:
        response = llm.invoke([{"role": "user", "content": prompt}])
        state["final_answer"] = response.content
        state["current_step"] = "general_answer_complete"
        
        # Add to conversation history
        if 'conversation_history' not in state:
            state['conversation_history'] = []
        
        state['conversation_history'].append({
            'question': state['question'],
            'answer': state['final_answer'],
            'type': 'general_query',
            'data_used': False
        })
        
        c.logger(__name__).info("General answering agent completed response")
        
    except Exception as e:
        c.logger(__name__).error(f"Error in general answering agent: {e}")
        state["current_step"] = "error"
    
    return state
