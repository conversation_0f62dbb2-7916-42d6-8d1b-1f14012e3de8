from agents.state import B<PERSON><PERSON>tate
from agents.logical.column_rag.rag import columnsDes<PERSON><PERSON><PERSON>
from config import Config
from models.base import model_backer, ModelBake

from prompts.general import GENERAL_ANSWERING_NODE_PROMPT

c = Config()
#llm = model_backer(ModelBake.SMALL)

def run_general_answering_agent(state: BOMState) -> BOMState:
    """
    Handles general BOM questions that don't require specific data
    """
    question = state["question"]
    
    context = columnsDescriptionRAG.build_prompt(question, top_k=5)

    prompt = GENERAL_ANSWERING_NODE_PROMPT.format(
        context=context
    )
    llm = model_backer(ModelBake.SMALL)
    try:
        response = llm.invoke([{"role": "system", "content": prompt}, {"role": "user", "content": question}])
        state["final_answer"] = response.content
        state["current_step"] = "general_answer_complete"
        
        # Add to conversation history
        if 'conversation_history' not in state:
            state['conversation_history'] = []
        
        state['conversation_history'].append({
            'question': state['question'],
            'answer': state['final_answer'],
            'type': 'general_query',
            'data_used': False
        })
        
        c.logger(__name__).info("General answering agent completed response")
        
    except Exception as e:
        c.logger(__name__).error(f"Error in general answering agent: {e}")
        state["current_step"] = "error"
    
    return state