import json
import pandas as pd
from typing import Literal

from agents.state import B<PERSON><PERSON>tate
from config import Config
from models.base import model_backer, ModelBake

from agents.logical.db import OracleSQLAgent
from agents.logical.rag.db import columns_rag

from structures.data_requirements import DataRequirement

from prompts.bom import (
    RUN_ENHANCED_RAG_PROMPT, CHECK_REQUIRED_DATA_PROMPT, CHECK_COST_SAVINGS_PROMPT,
    RUN_ENHANCED_ANSWERING_PROMPT, RUN_ORACLE_PROMPT, RUN_ORACLE_PROMPT_COST,
    RUN_RAG_COLLECTOR_PROMPT, RUN_RAG_NODE, CLARIFY_COLUMNS_PROMPT
)

from langgraph.types import Command, interrupt

c = Config()
slm = model_backer(ModelBake.NOVA_LITE)
# llm = model_backer(ModelBake.NOVA_PRO)

def safe_should_use_bom_data(state: BOMState) -> Literal["bom_query", "general_query"]:
    """Safely route based on data requirements"""
    try:
        requires_data = state.get("requires_data", True)  # Default to requiring data
        return "bom_query" if requires_data else "general_query"
    except Exception as e:
        c.logger(__name__).error(f"Error in data routing: {e}")
        return "bom_query"  # Default to BOM query

def check_columns_clearance(state: BOMState) -> Literal["clear", "clarify"]:
    try:
        if state["needs_clarification"]:
            return "clarify"
        return "clear"
    except Exception as e:
        c.logger(__name__).error(f"Error in column clearance: {e}")
        return "clear"  # Default to BOM query  

def check_requires_data(state: BOMState) -> BOMState:
    """
    Determines if the question requires actual BOM data retrieval
    """

    llm = model_backer(ModelBake.NOVA_PRO)

    prompt = CHECK_REQUIRED_DATA_PROMPT.format(
        state['question']
    )
    
    try:
        response = llm.invoke([{"role": "user", "content": prompt}])
        if "true" in response.content.strip().lower():
            requires_data = True
        else:
            requires_data = False
        state["requires_data"] = requires_data
        state["current_step"] = "data_requirement_checked"
        
        c.logger(__name__).info(f"Requires data: {requires_data}")
        
    except Exception as e:
        c.logger(__name__).error(f"Error checking data requirements: {e}")
        state["requires_data"] = False
        state["current_step"] = "error"
    
    return state

def check_cost_savings(state: BOMState) -> BOMState:
    """
    Determines if the question requires cost savings or bom data analysis
    """

    llm = model_backer(ModelBake.NOVA_PRO)

    prompt = CHECK_COST_SAVINGS_PROMPT.format(
        state['question']
    )
    
    try:
        response = llm.invoke([{"role": "user", "content": prompt}])
        if "true" in response.content.strip().lower():
            requires_cost_saving = True
        else:
            requires_cost_saving = False
        state["cost_saving"] = requires_cost_saving
        state["current_step"] = "cost_saving_checked"
        
        c.logger(__name__).info(f"Requires cost saving: {requires_cost_saving}")
        
    except Exception as e:
        c.logger(__name__).error(f"Error checking cost savings: {e}")
        state["cost_saving"] = False
        state["current_step"] = "error"
    
    return state

def check_cost_saving_need(state: BOMState) -> Literal["cost_saving", "other"]:
    try:
        if state["cost_saving"]:
            return "cost_saving"
        return "other"
    except Exception as e:
        c.logger(__name__).error(f"Error in check cost saving: {e}")
        return "other"

def general_question_check(state: BOMState) -> Literal["needs_data", "general_question"]:
    """Safely check for general question with proper key validation"""
    try:
        requires_data = state.get("requires_data", False)
        c.logger(__name__).info(f"requires_data> {requires_data} XX {state}")
        return "needs_data" if requires_data else "general_question"
    except Exception as e:
        c.logger(__name__).error(f"Error in general question routing: {e}")
        return "continue"

def run_rag_agent(state: BOMState) -> BOMState:
    """
    Retrieval-Augmented Generation agent that collects relevant BOM data
    """
    # This would typically integrate with Amazon Bedrock Knowledge Bases
    # For demonstration, we'll simulate the RAG process
    
    columns = state.get("selected_columns", [])
    question = state["question"]
    
    llm = model_backer(ModelBake.NOVA_PRO)

    prompt = RUN_RAG_NODE.format(
        question, ', '.join(columns)
    )
    
    try:
        response = llm.invoke([{"role": "user", "content": prompt}])
        state["rag_plan"] = response.content
        state["current_step"] = "rag_complete"
        
        c.logger(__name__).info("RAG agent completed data collection plan")
        
    except Exception as e:
        c.logger(__name__).error(f"Error in RAG agent: {e}")
        state["current_step"] = "error"
    
    return state


def run_oracle_agent_cost(state: BOMState) -> BOMState:
    """
    Oracle agent that executes data retrieval based on RAG plan
    """
    # Extract required info from state
    original_question = state.get('original_question', state.get('question'))
    refactored_question = state.get('refactored_question', state.get('question'))
    bom_id = state['bom_id']  # Ensure bom_id is passed in user input/state

    rag = columns_rag
    prompt_context = rag.build_prompt(refactored_question, 40)
    
    prompt = RUN_ORACLE_PROMPT_COST.format(
        prompt_context, refactored_question, bom_id
    )
    
    oracle_agent = OracleSQLAgent()
    conversation_id = state.get('conversation_id', 'default')
    
    result = oracle_agent.ask(original_question, bom_id, prompt, conversation_id)
    c.logger(__name__).info(f"Oracle Agent: {result}")
    # Attach results to state for downstream nodes
    state['oracle_results'] = result
    state["bom_data"] = result
    state['current_step'] = 'oracle_agent_complete'
    return state

def run_oracle_agent_other(state: BOMState) -> BOMState:
    """
    Oracle agent that executes data retrieval based on RAG plan
    """
    # Extract required info from state
    original_question = state.get('original_question', state.get('question'))
    refactored_question = state.get('refactored_question', state.get('question'))
    bom_id = state['bom_id']  # Ensure bom_id is passed in user input/state

    rag = columns_rag
    prompt_context = rag.build_prompt(refactored_question, 40)
    
    # prompt = RUN_ORACLE_PROMPT.format(
    #     bom_id, prompt_context, refactored_question
    # )

    prompt = RUN_ORACLE_PROMPT.format(
        prompt_context, refactored_question, bom_id
    )
    
    oracle_agent = OracleSQLAgent()
    conversation_id = state.get('conversation_id', 'default')
    
    result = oracle_agent.ask(original_question, bom_id, prompt, conversation_id)
    c.logger(__name__).info(f"Oracle Agent: {result}")
    # Attach results to state for downstream nodes
    state['oracle_results'] = result
    state["bom_data"] = result
    state['current_step'] = 'oracle_agent_complete'
    return state


def run_enhanced_rag_agent(state: BOMState) -> BOMState:
    """
    Enhanced RAG agent that uses refactoring results for better data retrieval
    """
    refactored_question = state["refactored_question"]
    answering_steps = state.get("answering_steps", [])
    execution_plan = state.get("domain_context", {}).get("execution_plan", {})
    selected_columns = state.get("selected_columns", [])
    
    llm = model_backer(ModelBake.NOVA_PRO)

    prompt = RUN_ENHANCED_RAG_PROMPT.format(
        refactored_question, answering_steps, selected_columns
    )
    
    try:
        response = llm.invoke([{"role": "user", "content": prompt}])
        enhanced_rag_plan = response.content
        
        state["domain_context"]["enhanced_rag_plan"] = enhanced_rag_plan
        state["current_step"] = "enhanced_rag_complete"
        
        c.logger(__name__).info("Enhanced RAG agent completed with structured guidance")
        
    except Exception as e:
        c.logger(__name__).error(f"Error in enhanced RAG agent: {e}")
        state["current_step"] = "enhanced_rag_error"
    
    return state


def rag_collector_node(state: BOMState) -> BOMState:
    """
    Enhanced RAG agent that uses refactoring results for better data retrieval
    """
    llm = model_backer(ModelBake.SMALL)
    question = state["original_question"]
    answering_steps = state.get("answering_steps", [])
    execution_plan = state.get("domain_context", {}).get("execution_plan", {})
    selected_columns = state.get("selected_columns", [])
    rag_executer = columns_rag
    top_k = 20
    prompt = RUN_RAG_COLLECTOR_PROMPT.format(
        question
    )
    
    try:
        response = llm.invoke([{"role": "user", "content": prompt}])
        enhanced_rag_plan = response.content
        prompt_context = rag_executer.build_prompt(enhanced_rag_plan, top_k)
        state["domain_context"]["rag_collector_node"] = prompt_context
        state["current_step"] = "rag_collector_node_complete"
        c.logger(__name__).info("Enhanced RAG agent completed with structured guidance")
    except Exception as e:
        c.logger(__name__).error(f"Error in enhanced RAG agent: {e}")
        state["current_step"] = "enhanced_rag_error"
    
    return state


def run_enhanced_answering_agent(state: BOMState) -> BOMState:
    """
    Enhanced answering agent that generates responses using refactoring context
    """
    original_question = state["original_question"]
    refactored_question = state["refactored_question"]
    bom_data = state.get("bom_data", {})

    # if len(pd.DataFrame(bom_data)) > 5:
    #     bom_data_sample = pd.DataFrame(bom_data).head(5)
    # else:
    #     bom_data_sample = pd.DataFrame(bom_data)

    # c.logger(__name__).info(f"Oracle Agent: {bom_data}")

    llm = model_backer(ModelBake.NOVA_PRO)
    
    prompt = RUN_ENHANCED_ANSWERING_PROMPT.format(
        original_question, refactored_question, bom_data
    )
    
    try:
        response = llm.invoke([{"role": "user", "content": prompt}])
        state["final_answer"] = response.content
        state["current_step"] = "enhanced_answer_complete"
        
        # Add comprehensive entry to conversation history
        if 'conversation_history' not in state:
            state['conversation_history'] = []
        
        state['conversation_history'].append({
            'original_question': original_question,
            'refactored_question': refactored_question,
            'answer': state['final_answer'],
            'data_used': True
        })
        
        c.logger(__name__).info("Enhanced answering agent completed comprehensive response")
        
    except Exception as e:
        c.logger(__name__).error(f"Error in enhanced answering agent: {e}")
        state["current_step"] = "enhanced_answer_error"
    
    return state

def ask_user_for_clarification(state: BOMState) -> BOMState:
    """
    sends clarification msg to user
    """
    try:
        question = state.get("question", "")
        original_question = state["original_question"]
        refactored_question = state["refactored_question"]
        clarification_message = state["clarification_message"]
        
        state["final_answer"] = clarification_message
        
        # Add comprehensive entry to conversation history
        if 'conversation_history' not in state:
            state['conversation_history'] = []
        
        state['conversation_history'].append({
            'original_question': original_question,
            'refactored_question': refactored_question,
            'answer': state['final_answer'],
            'type': 'enhanced_query',
            'data_used': True,
            'confidence_score': state.get('confidence_score', 0.8)
        })
    except Exception as e:
        c.logger(__name__).error(f"Error recommending columns: {e}")
    
    return state

