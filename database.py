import cx_Oracle
from config import Config

c = Config()

class DBPool:
    def __init__(self):
        self.db_pool = self.init_db_pool()

    def init_db_pool(self):
        return cx_Oracle.SessionPool(
            user=c.DB_USER,
            password=c.DB_PASS,
            dsn=f"{c.DB_HOST}:{c.DB_PORT}/{c.DB_SERVICE_NAME}",
            min=20,
            max=50,
            increment=10,
            threaded=True,
            getmode=cx_Oracle.SPOOL_ATTRVAL_WAIT
        )
DBPool_obj = DBPool()