import os
import json
from datetime import datetime
from config import Config

c = Config()

COUNT_FILE = c.MONTHLY_COUNT_FILE

def load_counts():
    if os.path.exists(COUNT_FILE):
        with open(COUNT_FILE, "r") as f:
            try:
                return json.load(f)
            except json.JSONDecodeError:
                return {}  # empty file, return empty dict
    else:
        return {}

def save_counts(daily_counts):
    with open(COUNT_FILE, "w") as f:
        json.dump(daily_counts, f)

def increment_count(max_count):
    monthly_counts = load_counts()
    current_month = datetime.now().strftime("%Y-%m")
    if current_month not in monthly_counts:
        monthly_counts[current_month] = 0

    if monthly_counts[current_month] >= max_count:
        return True

    monthly_counts[current_month] += 1
    save_counts(monthly_counts)
    return False
