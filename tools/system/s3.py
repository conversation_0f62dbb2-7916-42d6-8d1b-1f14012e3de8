
import boto3
import pandas as pd
from io import BytesIO

def upload_excel_to_s3(df, bucket_name, s3_file_name):
    # Initialize S3 client
    s3 = boto3.client('s3')

    try:
        # Create a BytesIO buffer to hold the Excel file
        excel_buffer = BytesIO()

        # Write the DataFrame to the buffer as an Excel file
        df.to_excel(excel_buffer, index=False)

        # Move the cursor to the beginning of the buffer
        excel_buffer.seek(0)

        # Upload the file to S3
        s3.upload_fileobj(excel_buffer, bucket_name, s3_file_name, ExtraArgs={'StorageClass': 'STANDARD_IA'})
        # Generate the S3 URI
        s3_url = f"s3://{bucket_name}/{s3_file_name}"
        return s3_url
    except Exception as e:
        print(f"Error uploading file: {e}")
