
class FileRegistry:
    def __init__(self):
        # Maps session_id -> list of file dicts with 'url' and 'title'
        self.session_files = {}

    def add_file(self, session_id: str, file_url: str, file_title: str):
        file_entry = {"url": file_url, "title": file_title}
        self.session_files.setdefault(session_id, []).append(file_entry)

    def get_files(self, session_id: str):
        return self.session_files.get(session_id, [])

    def clear_files(self, session_id: str):
        self.session_files[session_id] = []

# Singleton instance
file_registry = FileRegistry()