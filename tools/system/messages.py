import os
import re
import json


def update_memory(key, value, file_path="memory.json"):
    with open(file_path, "r") as file:
        data = json.load(file)
    data[key] = value
    with open(file_path, "w") as file:
        json.dump(data, file, indent=4)

def reset_memory(file_path="memory.json"):
    # Check if the file exists
    if os.path.exists(file_path):
        # Delete the file
        os.remove(file_path)
    
    # Create a new empty JSON file
    with open(file_path, "w") as file:
        json.dump({}, file, indent=4)

def reset_temp_data(file_path="temp_data.json"):
    # Check if the file exists
    if os.path.exists(file_path):
        # Delete the file
        os.remove(file_path)
    
    # Create a new empty JSON file
    with open(file_path, "w") as file:
        json.dump({"data": []}, file, indent=4)

def clean_text(text):
    """
    Removes text enclosed within <thinking>...</thinking> tags from the input text.

    Args:
        text (str): The input text.

    Returns:
        str: The cleaned text with the <thinking>...</thinking> blocks removed.
    """
    if "<thinking>" not in str(text):
        return text
    # Use a regex pattern to remove <thinking>...</thinking> and the enclosed content
    cleaned_text = re.sub(r"<thinking>.*?</thinking>", "", str(text), flags=re.DOTALL)
    # Escape dollar signs
    cleaned_text = cleaned_text.replace("$", "\$")
    # Strip extra spaces caused by removal
    return cleaned_text.strip()
