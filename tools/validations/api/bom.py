import pandas as pd
from dotenv import load_dotenv
import os
import cx_Oracle
from config import Config
import socket
# load_dotenv(override=True)
c = Config()
# DB_USER = os.getenv("QUALITY_DB_USER")
# DB_PASS = os.getenv("QUALITY_DB_PASS")
# DB_HOST = os.getenv("QUALITY_DB_HOST_1")
# DB_PORT = os.getenv("QUALITY_DB_PORT")
# DB_SERVICE_NAME = os.getenv("QUALITY_DB_SERVICE_NAME")

# oracle_connection_string = f"{DB_USER}/{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_SERVICE_NAME}"


def validate_bom(bom_id):
    sql_query = f"""
    select count(*) as count_parts from CUST_DATA.BOM_RESULT
    where BOM_ID = {bom_id}
    """
    connection = cx_Oracle.connect(c.db__connection_string)
    out = pd.read_sql(sql_query, con=connection)
    count_parts = out["COUNT_PARTS"].iloc[0]
    if count_parts==0:
        # bom not in DB
        return "Fail"
    return "Pass"

def get_server_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # Doesn't have to be reachable
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
    except Exception:
        ip = "127.0.0.1"
    finally:
        s.close()
    return ip
