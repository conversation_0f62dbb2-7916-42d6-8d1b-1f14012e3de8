from pydantic import BaseModel
from typing import List, Optional

class File(BaseModel):
    fileId: str
    fileUri: str
    fileTitle: str

class Image(BaseModel):
    imageId: str
    imageUri: str
    imageTitle: str

class MessagePayload(BaseModel):
    files: List[File]
    images: List[Image]

class OutputModel(BaseModel):
    conversationId: str
    messageId: Optional[int] = None
    messageText: str
    messageStatus: str
    messagePayload: MessagePayload
    generateReportFlag: str
    messageResponseTimestamp: str