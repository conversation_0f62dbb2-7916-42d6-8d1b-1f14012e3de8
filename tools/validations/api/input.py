from pydantic import BaseModel
from typing import Optional

class AuthorizationInfo(BaseModel):
    authorizationType: str
    authorizationToken: str

class ConversationInfo(BaseModel):
    id: str
    type: Optional[str] = None  # "old" or "new"

class InputModel(BaseModel):
    authorizationInfo: AuthorizationInfo
    conversationInfo: ConversationInfo
    messageText: str
    messageReceiveTimestamp: Optional[str] = None
    questionId: Optional[int] = None
    bomId: int
    optimizerId: Optional[int] = None