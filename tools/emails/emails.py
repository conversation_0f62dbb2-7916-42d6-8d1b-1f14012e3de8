import json
import pandas as pd
from datetime import datetime
from langchain_core.tools import tool
from pandasai import SmartDataframe

from data_repo import data_repo, LEVEL, DT
from model import (
    AgentBrain, LLM_RUNNER
)
from prompts import (
    email_data_instruction,
    email_drafting_instruction
)
from .utils import send_email

@tool
def send_email_tool(user_email_query: str):
    """
        A tool that drafts an email to be sent for a manufacturer or distributer
        The tool requires `user_email_query` and this query should contains 
        the manufacturer name to select the data based on it.
    """
    # need to get the data based on the email
    input_df = data_repo.get_data(LEVEL.L1, DT.BOM)
    sdf = SmartDataframe(input_df, config={
        "llm": LLM_RUNNER, "verbose": True,
        "enable_cache": False, "max_retries": 3,
        # "custom_instructions": bom_savings_instruction,
        "security": "standard"
    })
    result = sdf.chat(email_data_instruction.format(list(input_df["Manufacturer"].unique()), user_email_query))
    # need to draft email message
    agent = AgentBrain(model_id='us.amazon.nova-pro-v1:0')
    email_response = agent.invoke_model(
        email_drafting_instruction.format(
            user_email_query, result.to_markdown()
        ),
        {"temperature": 0}
    )
    
    send_email(
        [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ],
        'RFQ - Request Quote For Supplier',
        email_response
    )
    return f"Email for the request sent successfully with a draft"
