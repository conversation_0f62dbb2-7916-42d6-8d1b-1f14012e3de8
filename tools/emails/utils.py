from O365 import Account, Message
from dotenv import load_dotenv
import os

load_dotenv(override=True)

# Your credentials
client_id = os.getenv("client_id")
client_secret = os.getenv("client_secret")
tenant_id = os.getenv("tenant_id")


def send_email(
    recipient_emails: list,
    subject: str,
    body: str
):
    # Authenticate
    credentials = (client_id, client_secret)
    account = Account(credentials, auth_flow_type='credentials', tenant_id=tenant_id)

    if account.authenticate():
        print("Authentication successful!")
        # Create a new message
        message = account.new_message(resource='<EMAIL>')
        for recipient_email in recipient_emails:
            message.to.add(recipient_email)
        message.subject = subject
        message.body = body

        # Send the message
        if message.send():
            print('Email sent successfully')
            return True
        else:
            print('Failed to send email')
            return False
    else:
        print('Authentication failed')
        return False