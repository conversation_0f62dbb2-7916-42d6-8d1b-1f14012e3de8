import asyncio
import datetime
from collections import defaultdict
import os

from fastapi import Fast<PERSON>I, Body, HTTPException, status
from tools.validations.api.input import InputModel
from tools.validations.api.output import (
    OutputModel, MessagePayload, File, Image
)
from tools.validations.api.bom import validate_bom, get_server_ip
from langfuse import Lang<PERSON>
from langfuse.langchain import CallbackHand<PERSON>

from langchain_core.messages import HumanMessage

from tools.system.messages import (
    update_memory, reset_memory,
    reset_temp_data, clean_text
)
# from tools.system.user_preferences import get_preference
# from tools.system.analysis_session import analysis_session_obj
from tools.system.file_registry import file_registry
from tools.system.counter import increment_count

from agents import (
    create_fixed_enhanced_workflow,
    run_fixed_enhanced_bom_query
)
from agents.state import init_state
from memory.checkpointer.file import get_chat_history_local_file

from config import Config
from dotenv import load_dotenv

load_dotenv(override=True)

print(os.getenv("LANGFUSE_HOST"))
c = Config()

print("langfuse host")
print(c.langfuse__host)

current_ip = get_server_ip()
print(f"current_ip: {current_ip}")

os.environ["LANGFUSE_MAX_RETRIES"] = "1"
langfuse = Langfuse(
    public_key=c.langfuse__public_key,
    secret_key=c.langfuse__secret_key,
    host=c.langfuse__host
)

langfuse_handler = CallbackHandler()

# Initialize FastAPI app
app = FastAPI()
# Initialize graph app
graph = create_fixed_enhanced_workflow()

def process_conversation_message(input_data):
    # validate BOM
    validate_result = validate_bom(bom_id=input_data.bomId)
    if validate_result == "Fail":
        return "BOM not available, please try again later"

    initial_state = init_state(
        input_data.messageText,
        input_data.bomId,
        input_data.conversationInfo.id
    )
    config = {
        "configurable": {"thread_id": input_data.conversationInfo.id},
        "callbacks": [langfuse_handler]
    }

    with langfuse.start_as_current_span(name=input_data.conversationInfo.id) as root_span:
        root_span.update_trace(
            input=input_data.messageText,
            session_id=input_data.conversationInfo.id,
            version = current_ip
        )
        # Run the app once inside the span context so the callback handler uses this trace
        result = graph.invoke(initial_state, config)

        chat_history = get_chat_history_local_file(input_data.conversationInfo.id, conversation_dir=c.conversations_folder)
        chat_history.add_user_message(result.get("original_question"))
        chat_history.add_ai_message(result.get("final_answer"))

        assistant_msg = result.get("final_answer", "Unable to process question")
        assistant_msg = clean_text(assistant_msg)
        root_span.update_trace(
            output=assistant_msg
        )

    return assistant_msg


@app.post("/message", response_model=OutputModel)
def message(input_data: InputModel = Body(...)):
    # Get the conversation info
    conv_id = input_data.conversationInfo.id
    
    # Check and increment counter (outside the lock for efficiency)
    if increment_count(c.MONTHLY_LIMIT):
        exceed_msg = "Sorry, Exceeded Global Limit, please reach us to extend or try again later"
        return OutputModel(
        conversationId=input_data.conversationInfo.id,
        messageId=input_data.questionId,
        messageText=exceed_msg,
        messageStatus="success",
        messagePayload=MessagePayload(
            files=[],
            images=[]
        ),
        generateReportFlag="false",
        messageResponseTimestamp=datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
    )
    
    # Add logging to verify lock acquisition
    print(f"Waiting for lock for conversation {conv_id}")
    
    # Acquire the lock for this conversation
    # with lock:
    print(f"Acquired lock for conversation {conv_id}")
    
    # Clear file registry
    file_registry.clear_files(conv_id)
    
    # Process the message
    assistant_msg = process_conversation_message(input_data)
    
    # Check files
    registered_files = file_registry.get_files(conv_id)
    if registered_files:
        files = [File(fileId="",fileUri=item["url"],fileTitle=item["title"]) for item in registered_files]
    else:
        files = []
        
    print(f"Releasing lock for conversation {conv_id}")
    
    # This part is outside the lock since it doesn't need to be serialized
    return OutputModel(
        conversationId=input_data.conversationInfo.id,
        messageId=input_data.questionId,
        messageText=assistant_msg,
        messageStatus="success",
        messagePayload=MessagePayload(
            files=files,
            images=[]
        ),
        generateReportFlag="true" if files else "false",
        messageResponseTimestamp=datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
    )

@app.get("/health")
def check_health():
    return {"message": "Elyx is live"}
